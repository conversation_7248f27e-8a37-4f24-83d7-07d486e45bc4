app_version: 1.0.0
name: breachsense
description: App to interact with Breachsense - https://breachsense.io/doc
contact_info:
  name: "@davedhaval"
  url: https://www.infopercept.com
  email: <EMAIL>
tags:
  - Darkweb
  - Data breach Monitor 
categories:
  - Assets 
authentication:
  required: true
  parameters:
    - name: api_key
      description: The API key to use
      example: "*****"
      required: true
      schema:
        type: string
actions:
  - name: Basic_search
    description: Just a simple search
    parameters:
    - name: search_term
      description: search term - e.g. example.<NAME_EMAIL>
      required: true
      example: "<EMAIL>"
      schema:
        type: string 
    - name: date
      description: only display results newer that this value. Value set in YYYYMMDD format
      example: "20210820"
      required: false
      schema:
        type: string
  - name: Display_Description
    description: Display a short description of the breach
    parameters:
    - name: search_term
      description: search term - e.g. example.<NAME_EMAIL>
      required: true
      example: "<EMAIL>"
      schema:
        type: string 
    - name: date
      description: only display results newer that this value. Value set in YYYYMMDD format
      example: "20210820"
      required: false
      schema:
        type: string
  - name: Strict_search
    description: Display a short description of the breach
    parameters:
    - name: search_term
      description: search term - e.g. example.<NAME_EMAIL>
      required: true
      example: "<EMAIL>"
      schema:
        type: string 
    - name: date
      description: only display results newer that this value. Value set in YYYYMMDD format
      example: "20210820"
      required: false
      schema:
        type: string
  - name: Domain_Monitor
    description: manage monitored domains
    parameters:
    - name: action
      description: must be set to add, del or list
      required: true
      example: "add, del or list"
      schema:
        type: string 
    - name: domain
      description: add/delete the domain you wish to monitor
      example: "example.com"
      required: false
      schema:
        type: string
  - name: Custom_search
    description: Supply custom switch
    parameters:
    - name: search_term
      description: search term - e.g. example.<NAME_EMAIL>
      required: true
      example: "<EMAIL>"
      schema:
        type: string 
    - name: date
      description: only display results newer that this value. Value set in YYYYMMDD format
      example: "20210820"
      required: false
      schema:
        type: string
    - name: extra_Params
      description: only display results newer that this value. Value set in YYYYMMDD format
      example: "list&uniq&hash&p=2"
      required: true
      schema:
        type: string
  - name: Check_credits 
    description: return the number of remaining monthly queries allowed
    parameters:
    required: false
    schema:
        type: string
large_image: data:image/png;base64,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
