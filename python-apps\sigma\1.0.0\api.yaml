app_version: 1.0.0
name: Sigma
description: A way to translate SIEM queries with Shuffle
contact_info:
  name: "@frikkylikeme"
  url: https://shuffler.io
  email: <EMAIL>
tags:
  - Testing 
categories:
  - SIEM 
actions:
  - name: get_searches 
    description: Runs a python script defined by YOU 
    parameters:
      - name: engine 
        description: The engine to translate to
        required: true
        options:
          - uberagent
          - sumologic-cse
          - netwitness-epl
          - netwitness
          - stix
          - es-rule
          - graylog
          - sqlite
          - sysmon
          - csharp
          - logpoint
          - mdatp
          - ala-rule
          - humio
          - crowdstrike
          - sumologic-cse-rule
          - elastalert
          - limacharlie
          - carbonblack
          - ala
          - arcsight
          - sql
          - logiq
          - grep
          - fireeye-helix
          - fieldlist
          - xpack-watcher
          - splunkxml
          - kibana
          - powershell
          - arcsight-esm
          - kibana-ndjson
          - qradar
          - qualys
          - es-qs
          - elastalert-dsl
          - splunk
          - sumologic
          - es-dsl
          - ee-outliers
        example: 'kibana'
        schema:
          type: string
      - name: backend
        description: The backend to use. If blank, space or list, this will return a list of options
        required: true
        multiline: false 
        schema:
          type: string
      - name: shuffle_category
        description: The Shuffle
        required: true
        multiline: false 
        schema:
          type: string
    returns:
      schema:
        type: string
large_image: data:image/jpg;base64,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
