app_version: 1.0.0
name: Office365 Mgt API
description: Collect AzureActiveDirectory,Exchange,Sharepoint,General, DLP audit logs
tags:
  - Audit
categories:
  - Audit
contact_info:
  name: "@RobertEvans"
  url: https://shuffler.io
  email: <EMAIL>
authentication:
  required: true
  parameters:
    - name: planType
      description: Enterprise, GCC , GCCHigh, DoD
      example: "Enterprise"
      required: true
      schema:
        type: string
    - name: tenantID
      description: xxxx-xxxx-xxxx-xxxx
      example: "xxxx-xxxx-xxxx-xxxx"
      required: true
      schema:
        type: string
    - name: clientID
      description: xxxx-xxxx-xxxx-xxxx
      example: "xxxx-xxxx-xxxx-xxxx"
      required: true
      schema:
        type: string
    - name: clientSecret
      description: xxxx-xxxx-xxxx-xxxx
      example: "*****"
      required: true
      schema:
        type: string
actions:
  - name: run_o365poller
    description: Run office365 audit poller for defined interval
    parameters:
      - name: json_data
        description: The JSON to handle 
        required: false
        multiline: true
        example: 'No args for now, insert credentials and get returned data'
        schema:
          type: string
      - name: PollInterval
        description: The selected python function to run
        required: true
        multiline: true
        example: '1'
        options: 
          - poll_10min
          - poll_23hours
        schema:
          type: string
    returns:
      schema:
        type: string
large_image: data:image/png;base64,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
