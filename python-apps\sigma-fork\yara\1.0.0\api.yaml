app_version: 1.0.0
name: Yara
description: Analyzes  files based on set rules
tags:
  - Intel 
  - Malware 
  - File check
categories:
  - Intel 
contact_info:
  name: "@frikkylikeme"
  url: https://shuffler.io
  email: <EMAIL>
actions:
  - name: analyze_file
    description: Gets findings for a specific region
    parameters:
      - name: file_id
        description: The file(s) to run towards
        required: true 
        multiline: false 
        example: 2ff4c409-f66a-4bdc-bede-5dd5969a8c55
        schema:
          type: string
      - name: timeout
        description: The timeout per rule. Default 15
        required: false 
        multiline: false 
        example: 15
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: analyze_by_file_category
    description: Verifies if the file matches a certain rule
    parameters:
      - name: file_id
        description: The file(s) to run towards
        required: true 
        multiline: false 
        example: 2ff4c409-f66a-4bdc-bede-5dd5969a8c55
        schema:
          type: string
      - name: file_category 
        description: The rules to run from files
        required: true 
        multiline: false 
        example: 'rule dummy { condition: true }'
        schema:
          type: string
      - name: timeout
        description: The timeout for all rules. Default 15
        required: false 
        multiline: false 
        example: 15
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: download_rules 
    description: Downloading rule testing
    parameters:
      - name: namespace 
        description: The File-namespace in Shuffle where you uploaded the rules 
        required: true 
        multiline: false 
        example: yara
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: analyze_by_rule 
    description: Verifies if the file matches a certain rule
    parameters:
      - name: file_id
        description: The file(s) to run towards
        required: true 
        multiline: false 
        example: 2ff4c409-f66a-4bdc-bede-5dd5969a8c55
        schema:
          type: string
      - name: rule 
        description: The rule to run
        required: true 
        multiline: false 
        example: 'rule dummy { condition: true }'
        schema:
          type: string
      - name: timeout
        description: The timeout per rule. Default 15
        required: false 
        multiline: false 
        example: 15
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: custom_rules 
    description: Verifies if the file matches a certain rule
    parameters:
      - name: file_id
        description: The file(s) to run towards
        required: true 
        multiline: false 
        example: 2ff4c409-f66a-4bdc-bede-5dd5969a8c55
        schema:
          type: string
      - name: namespace
        description: The rule to run
        required: true 
        multiline: false 
        example: 'rule dummy { condition: true }'
        schema:
          type: string    
      - name: timeout
        description: The timeout per rule. Default 15
        required: false 
        multiline: false 
        example: 15
        schema:
          type: string
    returns:
      schema:
        type: string      
large_image: data:image/png;base64,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
