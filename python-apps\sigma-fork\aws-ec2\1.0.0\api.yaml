app_version: 1.0.0
name: AWS ec2 
description: An app to interact with Amazon EC2
contact_info:
  name: "@shalinbhavsar"
  url: https://shuffler.io
  email: <EMAIL>
tags:
  - Network 
  - Assets
categories:
  - Network 
  - Assets
authentication:
  required: true
  parameters:
    - name: access_key 
      description: The access key to use
      example: "*****"
      required: true
      schema:
        type: string
    - name: secret_key
      description: The secret key to use 
      example: "*****"
      required: true
      schema:
        type: string
    - name: region 
      description: The region to use
      example: "ap-south-1"
      required: true
      schema:
        type: string
actions:
  - name: block_ip 
    description: Creates a new firewall entry to block an IP
    parameters:
      - name: NetworkAclId
        description: The NetworkAclId to edit
        required: true
        multiline: false 
        example: 'acl-0f91affa651ff09f4'
        schema:
          type: string
      - name: ip 
        description: The IP to handle
        required: true
        multiline: false 
        example: '10.0.0.0'
        schema:
          type: string
      - name: direction 
        description: The direction the rule applies to 
        required: true
        multiline: false 
        example: '{"data": "testing"}'
        options:
          - inbound 
          - outbound 
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: get_rules 
    description: Gets the rules for an ACL ID
    parameters:
      - name: NetworkAclId 
        description: The NetworkAcl's id identifier.
        required: true
        multiline: false 
        example: 'acl-0f91affa651ff09f4'
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: create_acl_entry 
    description: Creates an ACL entry 
    parameters:
      - name: NetworkAclId 
        description: The NetworkAclId  id to edit
        required: true
        multiline: false 
        example: 'acl-0f91affa651ff09f4'
        schema:
          type: string
      - name: cidr_block 
        description: The IP's to handle
        required: true
        multiline: false 
        example: '10.0.0.0/24'
        schema:
          type: string
      - name: dryrun 
        description: Test or not
        required: true
        multiline: false 
        example: '{"data": "testing"}'
        options:
          - true
          - false 
        schema:
          type: string
      - name: direction 
        description: The direction the rule applies to 
        required: true
        multiline: false 
        example: '{"data": "testing"}'
        options:
          - inbound 
          - outbound 
        schema:
          type: string
      - name: portrange_from 
        description: The JSON to handle 
        required: true
        multiline: false 
        example: '12344'
        schema:
          type: string
      - name: portrange_to 
        description: The JSON to handle 
        required: true
        multiline: false 
        example: '12345'
        schema:
          type: string
      - name: protocol 
        description: The JSON to handle 
        required: true
        multiline: false 
        example: 'TCP'
        schema:
          type: string
      - name: rule_action 
        description: The JSON to handle 
        required: true
        multiline: false 
        example: ''
        options:
          - allow
          - deny 
        schema:
          type: string
      - name: rule_number 
        description: The selected python function to run
        required: true
        multiline: false 
        example: '120'
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: instance_state_change
    description: Termiante/Start/Stop an EC2 Instance
    parameters:
      - name: instance_id
        description: Instance ID to change
        required: true
        multiline: false
        example: 'i-0bec2a0bf000bb71c'
        schema:
          type: string
      - name: action
        description: Action to perform on EC2 Instance
        required: true
        multiline: false
        example: 'terminate|start|stop'
        options:
          - terminate
          - start
          - stop
        schema:
          type: string
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string  
  - name: create_network_interface
    description: Creates a network interface in the specified subnet.
    parameters:
      - name: description
        description: A description for the network interface.
        required: false
        multiline: false
        example: 'A description for the network interface.'
        schema:
          type: string
      - name: subnetid
        description: The ID of the subnet to associate with the network interface.
        required: true
        multiline: false
        example: 'subnet-1491c1e8d873c06de'
        schema:
          type: string
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: create_image
    description: Creates an Amazon EBS-backed AMI from an Amazon EBS-backed instance that is either running or stopped.
    parameters:
      - name: description
        description: A description for the new image.
        required: false
        multiline: false
        example: 'An AMI for my server'
        schema:
          type: string
      - name: instance_id
        description: The ID of the instance.
        required: true
        multiline: false
        example: 'i-0bec2a0bf000bb71c'
        schema:
          type: string
      - name: name
        description: A name for the new image.
        required: true
        multiline: false
        example: 'My server'
        schema:
          type: string
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
      - name: noreboot
        description: 
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: deregister_an_image
    description: Deregisters the specified AMI.
    parameters:
      - name: image_id
        description: The ID of the AMI.
        required: true
        multiline: false
        example: 'ami-0fa80fbg05d0c7e49'
        schema:
          type: string
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: create_snapshot
    description: creating a snapshot
    parameters:
      - name: description
        description: A description for the snapshot.
        required: false
        multiline: false
        example: 'This is my root volume snapshot.'
        schema:
          type: string
      - name: volume_id
        description: The ID of the EBS volume.
        required: true 
        multiline: false
        example: 'vol-1234567890abcdef0'
        schema:
          type: string
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: delete_snapshot
    description: deleting a snapshot
    parameters:
      - name: snapshot_id
        description: The ID of the EBS snapshot.
        required: true
        multiline: false
        example: 'snap-07cfcb1eb6d1a3df8'
        schema:
          type: string
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: delete_network_interface
    description: deleting a network interface
    parameters:
      - name: networkinterface_id
        description: network interface id to delete
        required: true
        multiline: false
        example: 'eni-049b032ab651c9cd2'
        schema:
          type: string
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: describe_address
    description: Describes the specified Elastic IP addresses or all of your Elastic IP addresses.
    parameters: 
      - name: publicips
        description: One or more Elastic IP addresses.
        required: False
        multiline: false
        example: '***********'
        schema:
          type: string
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: describe_keypair
    description: Describes the specified key pairs or all of your key pairs.
    parameters: 
      - name: option
        description: KeyNames or KeyPairIds
        required: false 
        multiline: false
        options:
          - KeyNames
          - KeyPairIds
        schema:
          type: string
      - name: value
        description: Option's value
        required: false
        multiline: false
        example: 'my-key-pair' 
        schema:
          type: string 
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: describe_networkacls
    description: Describes one or more of your network ACLs.
    parameters:
      - name: networkAcl_Id
        description: The ID of the network ACL.
        required: false
        multiline: false
        example: 'acl-5fb85d36'
        schema: 
          type: string 
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: describe_securitygroups
    description: Describes the specified security groups or all of your security groups.
    parameters: 
      - name: option
        description: KeyNames or KeyPairIds
        required: false 
        multiline: false
        options:
          - GroupIds
          # - GroupNames
        schema:
          type: string
      - name: value
        description: Option's value
        required: false
        multiline: false
        example: 'string' 
        schema:
          type: string 
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: describe_vpc
    description: Describes one or more of your VPCs.
    parameters: 
      - name: vpcid
        description: One or more VPC IDs.
        required: false
        multiline: false
        example: 'string' 
        schema:
          type: string
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: create_an_instance
    description: creates an ec2 instance.
    parameters: 
      - name: dryrun
        description: Test or not
        required: true
        multiline: false
        example: 'True|False'
        options:
          - True
          - False
        schema:
          type: string
      - name: image_id
        description: AMI ID.
        required: true
        multiline: false
        example: 'ami-0969b41569eb56' 
        schema:
          type: string
      - name: min_count
        description: min count
        required: true
        multiline: false
        example: '1'
        schema:
          type: string
      - name: max_count
        description: min count
        required: true
        multiline: false
        example: '1'
        schema:
          type: string
      - name: instance_type
        description: instance type
        required: true
        multiline: false
        example: 't2.micro'
        schema:
          type: string   
      - name: user_data
        description: user data.
        required: true
        multiline: true
        example: 'user data text' 
        schema:
          type: string
      - name: key_name
        description: key name.
        required: true
        multiline: false
        example: 'key_name' 
        schema:
          type: string     
      - name: security_group_ids
        description: Secuirty group ids
        required: false
        multiline: false
        example: 'multiple id seperated by space' 
        schema:
          type: string                
    returns:
      schema:
        type: string      
        
large_image: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAK4AAACuCAYAAACvDDbuAAAgAElEQVR4Xuy9B5RUVfY9vF/lXJ1pchIUUMCcnVEZ8yiKEZUoBlBEDIwBs6gIShITSlBUFNOoo2NCMIw6jhkjmYamc1dXzv+1z32vusAGtBl/87G+rrWabrorvHfvvuees88+52o9B/wpi7ZH2wjsZiOgtQF3N5uxtsuVEWgDbhsQdssRaAPubjltbRfdBtw2DOyWI9AG3N1y2touug24bRjYLUegDbi75bS1XXQbcNswsFuOQBtwd8tpa7voNuC2YWC3HIE24O6W09Z20W3AbcPAbjkCbcDdLaet7aLbgNuGgd1yBNqAu1tOW9tFtwG3DQO75Qi0AXe3nLa2i24DbhsGdssRaAPubjltbRfdBtw2DOyWI9AG3N1y2touug24bRjYLUegDbi75bS1XXQbcNswsFuOQBtwd8tpa7voNuC2YWC3HIE24O6W09Z20W3AbcPAbjkCbcDdLaet7aLbgNuGgd1yBP4/Adwd9zn9vV1QtRYmYtvfGf/ne//e929++5Y+6X+BgtbegbYL977zUWjpqlr6HUfx94/kLgO3tYNm3Hh224vW8t/R+HlHg5B/08bPGpDN+zn3GXmDxL/LZ6X0762AXLY1Q7715/z+Kdv69bL0Wv0mHP1dWLzywaZtgJf/fhyg/Dnc9mcTtKxZf4/fN/7/U+DKbWjbrvttQbq9gTWe15I1NQBqADh/Ves/C3AzgJYCkPl9o6Y/Wz55F8HbaszpewVvo7XGQ418phX2zhgufjiB19IOZoDWmL+8eRQwZ4EsgWv5XwG39UOfW387fIudWYRtpy0PtBxQsQotAFfGnsBN//8cuL/a8377Iv6Vxd12t9wBeOVTtP+lxW2dj9LsKuxknLZyHbZ9bv5qVgOhHnlWNf//WwFYN5e74irwk1pr7vKt9m+Hyq+eKXtFq21HdhevX3cVcm7XtrOaNz8tugwGcH//DeyaqyCTtq2P89tnQbkKO9vqdoQMrmhOHZ+j33yLvm0emHNja/i4tLitQ1/+Mvntd/3ffebO9qMdfVrz9f9+4Mj75uZ/OwbF8KVy47ttzLLtbvjbx2YXgdv6D95qbe5w3HaAbFnF+cDLf6Pt+b76J+eshAH83z5o+c9s5ZS37sNaeFXrllzzG2mtj+y2ccF2thsatqWlK/79o/i/B+6v9tptwGeY5V9NGp+X76Pu6OZ3ZBtbF5gZl/P7h/y/htlmo7cLb/nHA3dHF9f6yPa/AlytlU6WbHOy1bdkwwxL25IPnRfFCivwW+3OtjD7ra/b/uDL1bXybba7Jn8HEFv50blPIHBbv/h29MqdX5l6RuvAu8vADQVDcNidcDgcMJvNyGQy8mUymeQrlUrlfieepqbJVzabRRYZWGxmpNMppFJp+R1fYzZZoGn0nSG/N5stMJnM8rcsWZRMVr7Tv81mkztEjsViQSIRl/e22+3yPZFI5P6fTqv3N65JhjKbRTKZlC/e144e4WAIHo9H3pvvxddyHPhzLBaD0+mUz+OD18Lf8/98jvwtyev/HUjd5ql8T14nP5c/c7zj8bj87Ha7c2PPz+S8WK1W2Gw2+TkRTyCTzsj/+Xvjvvk3vp6/i0ajubnkGPHBezC+OD58HV/D36m51+S1vEfeHD+br+Xn8GFci/y9lbH9LgGXlqawoBixaBSRSEQGTYAnFwy5CQ4AL1qBTt0gv8vDxPWWQiabEaA2gzOLTDqLdDqDwsIihEJh+eJdOh2u3ATF4lE4HFbdZdh28tVncLA4UMlk8+CZzSYZ5GQyJaDld04+r5Ng4mTwZwNk24MV75/giEYi8hm8R77OGAeCORwOy3saYOHfOSYch2gspmzOLgCXn8Xx5PsSLPxM/kzw8otzwevi3/id18kFxZ+ddoc8PxFLyHN5Tcbz84FpgNIwLLx+fpktJoRCQVgsZnkfm82eAybvm+9ZUlKKWDSeGx+OgzG2aZn31t3/LgM3nUzJqjUsTfNKU5aLg8TB4I3mA1fAbNGQQRrpDAdfwUNZWgWaTAYCYIeDYHLK3wiyVDIlN2+xmpFK0ZrpsXUOAM3Rq7GQstmMAIoPA7gcWJfLC7vdIRNrWEn+3rCQuUXWAnoJ3CwtTVKBh5PCL36muhe1s4h1SyRylt4AOCfOxl1gF4DLceWD95a/YIydxQC2cW38P60o79fn8SAcCiGbJoDUgjKu33g/jolheIwhMHbVTDYNh8MuOxrHTI2BXRY+gUyjsGXLFricbhljYxzUHJgBk4Y0DVkr7n8XgZtFitupzS4DwQvjDdB6GWA1AGtMoGFxxVXQMnB5HEilkwIaAtZsIsi5zVgFqBazFcFgGIFAk7gNBLDT4ZRB5jazFXBz4GpONRIwVqtFBlhZHDXIXOkczEAgBIvZpg+02uJ5/QZwje1xe1aXoHW7XPJnWhk+aGF5f/x/UVERGhsb5T35e44Lf5at3ONBMBxq1cTl36rFbN7KPeH7EzjcDQwA8jvvhZ9v7Igcg3gsApu+rRs7jHKflLExXDtjsckOkds5M3A4uP3TdVM7JD8nkVBuFufL7y+QcaZtiUZjuXuXXYevY9L5fwFcs0njwtnKzzG2LV4cLa7h+3AwuKINK5HNplFbXyVWV0Brtih/VjMjm9WQydBvs8Juc8DhcIlV5qCkksoKiE+XJshayNjov3O6HLKdZTJp8UU5WdFoREDs8xUgmzGhsbFJQMbFx8nm3wyrskOLS5DalVthWBNj0ml1eK8NDQ2CMcPyG8+VrZj+oMXcqolTPoay+AQLF4XX6xVg8DN53cb/+Tc+aGl5nQS1uCqREJwOGgi1DAz3gNdqXD/fm+9puB6GBVf3RIuZhM2mXBRlcNQcWi1WWK02+ZluHg2PETPweriTRmIxWG02ZI0L2J51aGm325WzfDWuvHQC6ZSyULx4I5ghEEKhkAxG/jbEvxMgso0ijZKyQnBnpX9L8NIVCIciCASCiESiqK2tg89boK9cp6zqtB6w8X24MHLA3YrwVmAuL2+H6poqhMMhsbp2u/J5UylaPRsyaW7rygck0Dgx9Nd5fbxWw73Y3phazRY0NTWJhfL7/fL82tpaAUZBQYFMeGFhobzcAHFxcbF8VigcRiKVbDVw6arwMwKNjfI5LpdLroMA5bjzM3gttPpckIa7YgTOmQzdvDg49IaFNQJqvgffk+PL9+W98Yug43MUyFOwWEyIx2My1wRnOByRvxHE/IpGYmJxPR4vnE5XblemETKZLbrF/f0md9dcBWTgtFu57HNBF1e6sUp5A5yk8vJy9OrVC71790a3bt3QqVMntGvXDj6/DfFks8iFFjYWSyAYDCHQ2IRIJIaPP/4XVq78AV9+8TWqq2vk5n0+v7gUtL7qYTjIxs/N+aQ1a1ajqLgQBQV+sRC0vHw+gcVFgixdHOQCm3wfkBZLLYztP8waJy6+lX9pbLH8DC4CY5cxIn7DKpu4HdusuwTc6upqeNzunKvGBaesn4opCFguLF6j4b/S8klw5rQjFmuC1aoCOAJbuVGaWGvuUEcffTRKS0vRvXt3+eK8uVxkEpSFJnAj0ZAsyi1bqrB2zTqsXPm9fFVUVKgdMpWRXdPlcovVDQQCYqg8Xh9icS7c/2vgZjNIxsOw21SAwEniKuTNDRgwAHvuuSdOPvkklJaVorjQnZv9aFwBndGo1coATTEQwiwwE2uEW9wK6a5qwKZNdXj3nffw8suv4OuvvxELUVJaJla5WSZivNL4qCwKC/w49dRT8Oc/HQWzWUMw2ChW1+2i9QZWrd6ADz74GB9/9DGCwaBaFGYzgqGwuA8er2c7LLG+WLJZFPoLEAw1oaGhHgP674OhQ8/D3nv3EytPUBFcNptDGJLvv/8Bzz33PFauXAmX24NoItn8/r8KLrdeMPl8sfHUnj264dhjjsYBB+wv45lIJuB2ewTIXPjLl6/AsvdXCJBcbrcAktYxnozD7XIgEQ/D41ZWlC6V2WTCwIEDcc455+D44wZBn1q5EOMzM1nOF7+yMn98JFKKoTFrQDoL1NbVo6GhEZ988ineeusdfPjBR+LzFhYViwvB10qwnSar8AcAN5/KMrZNw6IgnYQpm0AiprYmWtW+/fpg3Lix6NG9E2rrAigp9suNqTRDFib99jMgeLNIxukj2WAxq0jceKR0y03gOmxWBCPcshxikWfPnoN58+bBbLHBYnPCZKbLkBDQcbuKxeJi5Ss3V+Lggw7ArJkPoGO5B4lYFk6Hhgzdl2xariajWdDQGMPUqfdhzpwH0av3XojHSdFlYbXZwUlSwQMDCZVt0H8SLWsmlYLdZkVToAHty0uxePFClJYUwu0iNZSFVXeBuLiqa+pk4urrm3DccSeiKRiBr6AUsUQS6UxawEaKkG4Mx4dBJVduPBqDz+tFJpUW6tFmscq4WS0mzJ49HX866lB5DalBXlswHITH7UM0loLZYsFVV92Ip55Zgo6dugiLEYlFYHfaEAoG4LKZkc2kUVW1BfsOHIhbJt+MIw4/QBa1hYE/507/OZtW7IVVR3M0HIHD7ZKx4r1qJiYzlFgyLeMLxOIJ2G1OvLdsOV5//R9Y+sJLAuBevfbEps1bYHd6kNU5+x1ubdv88Te5CgYHa0SmBK78LpVAOFCLAfv0w3HHHYcLLjhf/CEG/DojhGSKtFZKBpSUFBeX8rGYiDDDoilrzQfBlxK/SfGCxoODkBLWAbBZFIn9/or3MWPmbHz48b9RWt4BNqsDNbV16Ny5q0SvjY0BYTtKS4qw4InH0L1LO1jNQJp8bSYJq92KNJe7xYF4Enh68RJMnnwLiopLkcmqRAcXBBdQPmCzhpZUX4icVYfdisaGWhx+2MGYNXMaXE4rHDYzhOxLkR82wWZ1oikUgdfjRyIFXHzJWPz731/BbHUjllDjY9aRkpSAU3HgkpxJJuF2OMFVlE4QsFYEA00Y2H9vzHvsQZSV+ZBIxGCm9dMyiESjcDv9CEcT4pOeOvhCfPHlt3B5fbA5HahvrEdpu2KYTVnUVW6G1+3C+ecPxbhx4+BzWREMxQWwXo8dyURaFqfVYoaJQWgqDYtdzUE0GISF88Q5NZO/N+n6agVkJVNXGQb+Zv3GSjz77HN45NHH4HJ54HJ7EQzH/xjgGqQ0L9QAbi64IoeajuKWm27ASSedBK9XgS0cTiCRjCuLIUkAK2z6zwYYk+mEsAOktuLxpPhXtLwEgazYTCZHL9Hq09/io6qmCj6fT6zT1998h+v+NhnrN26Wgaivb0DnLt0EuJs3V6J9u3IEGuvxxLxHcPAB+8HrNiESCsNm1mBz2JAiYMwOmCzAN9+swsgRoxEKR2EXBoODbWqW8OiWVgFXWWD5N52Uaw42NWDc2EswccI4JJMxWSQmLQuzzjiYTFZkwAVhlsU7a/ZjmDHzQbg8hUjqWW9OPt8/lVFpbAJBMlEawGVA4Fo0ZhZNqKmqwZVXjMWk68YqpiQeAalRAoxvp4F0kxlfffMzzjxzKFyeAoniC4oLBbipTALpRAwFbiduv+VmnH76KWCMzZ3T5TCLlaUbRtZIOGCLiaEMGhsaZeF4fG6l7MtAjArZHWEcZAGaYLFahDEJSbBGLt4lO9Mvqzbg/AuGoaqqGoVFJbKI/xCLawQSnCQOIh+ypfGRTqJDuwI8v+RpFBZ4ZGuiX2i4Byl9i+FTE0mVAuXN0YeUCJ9bbDAiVsFqUasyFFIZOLfbJWDnVk3ulZwfqSc+mkJNYrXdLg/eem8F7ppyL9at24B25R3Q0BDIrXKPy41NFRsw6dqrMX7cxXDaobZYshgWTX5OazZY7WaEQmmce85QfPPtShnQTEZDIpmCialQfqhIJ8g9b029pZNxsVy0rrNmTsdfjjkKCR24dBOM+g4uAn5FY0nYHXYse/9jDBs+Gh5fMbKazpcSqNmMbLMEo+JQIeOQiicEuE4mLNIZBBoaZSf5858OkGDW4bIKaJlGFyPDHUOz4b7pD+K+6bPRo2dvVGzajHbt20Ez07f/GUUFXjz4wDScdNwx8pqGxiAcdodQZKlUFjaruvp4jJRYChahuZgKVtPf0BCCz6cMilRiEMV8RY4lUss7JbhhNo/pfw1Dzjwbq1evEVfPbHX8McAliAyfViUJVM5dLjYVR68eHfHE448KVSIcoU2lewlaBXblNvDL8MGNgEvHgzxXnqP76IlkRrhWvp9LDxwIXqGsLGrU6AOGxe914YFZD+OBB2aiQ8fOqKioREFhkQQAyXgCgcYGHHzg/nhy4RNw2DSQBFG+KVOcJOToJ6trnjDhBrz40iviLpDfpe9pYnSuX7gCrrovI1uXSkSRTMTQqWM5Fsyfh149uiCeiMFm0XT/luwFAzBSbg5EY3Gh2bZUN2DImeeiqiYAs5VZJlrKrJ5JYuCigMvP4QJPRGOyTbudDvF5/V4fXlz6HDp1LBAKyu1xCGiT6Tg0Mi7JDJIpDWPHTsRn//4KPn+RANfhssPjcSEaC2LM6BH424RxqKmqRklJiVj4VJpajiRcThsSSRV8cdr1LD5IxHBOKTsgTuPRtGQw6cpL+pZzwz/IfPO1GVjNXFBAbX0TNM2K008fgsrKLRKcquBs6/jGGOEdfd+pj6soDwVUI+vFQZZMSSomwJ3/xGM5DtduNcvNEZzi6+o3U1sblO177dq1WLVqFdatXS8cI2+aFFVxcRH69euHgQMHoGfPnigt8Yt3lMpkBcRet2IlNlVuht/vg4euQSCAAr8fW2oCGDlqNL7//ifY7E506NAJjQ0BoXb4+ZlkAk8vXoT+e+8lVpfATcYjikfUbNAYCgNYvPhFTLn7XlhtpHvUJLIiywCreG5bATeDbDqBYFMjjjn6KMyeNQOFPpdE9hYtC5tFNnjx22mTzWYbUmmlB0hlgCvGX4M331oGm8Ot/FmmpYkSJnVE28EANi2WlL5tMh4XfzTUFMSfjjoKD82ZDbqb2SwDJoIjjWAkCAf5XNiwduNmDB58LsgaktWg+9XU1Ih0JoHDDj8Yjz38IDKxEHwuJxxOumwJNAVDKCkplvEINEXg9blQVdWINWvX48svv8LH//oEFRWbYLFa4bDb4LDRAlvgcjlRVFKErl27oG/fPui3d1+UlhQjHI0L9eWw8/4UHk488Uz8smoVHE66G82G4bcA1njOToFr+LiGMMIQWvD3qWQUvXt2wmuvviQ0SDJNdRfz80AsmpQbfOWVv8vqWrVqNSo2VqCpiVksRYVwVpuCjeIW8ItZIJLZZASOP/44DBp0DI48/GCEoySxbbCYTEK5CU/Jyc9mxNXw+zyYN38x7rzrHrjdXhQWlkg2jOwBaa/1a9dg4lXjceP148UkpJMJ2Kz0H7NIa4rDpIxh3bqNGD5iNAJNIQGuxWpHItWcS28JuLSsNdWVuHzcZZh03VVq18hmkE0lYaMlkpFWQYraME1IMIFiMWPuQ49j6rSZcLp9kkEiaJMUKumpVpVaJQOShdVsQiIehcfpQqChAVdPvApXjB0l4+x2WxGNR2G2mhBPxOF0uMW/ffnVNzBq1GUoLe2Qo8gaG+ukzu7222/GuUP+CosEzBmx5twN3B6fWMdwmFktB5Y8txQrPvwIn3z6b6G46GfY7A6xyg31dfC4HBKAMqbhQnO7aTjao3uPbjKPw4cPFyalQ4eOcNh5fcDZZ5+PX35ZJe/FAPgPocPyFV75SiQDuN26lOOlF5+TLZtAoNNN7m7F8g/xzjvvSvaEK5kUCLdvCi74O26fsXgcPr8bsVhUCTToy6WSiETCsu24PS5cP+k6nHbaXyXRweDCrcsMGxobUFhQiFgqI8HA+vWVuODC4aiqroVJswrZzc/kglj1y0844rCD8eorzyAajiOdiqPA7xVQJNIZWKw28b/IRx93/GlYt75CsmmMehVwlZltCbhupxVVWzbhrjtvx7Dzz5bn0PFIJuj7KsjKYjdbxGemBJQuCAPWN99ehosvvUKAy+wSQUsWhkENF7bitjVk0ym4nXbEY1F4XS40NtRjzqxZOOG4I5GIUS9gQiQWhY2+aSYNs8mGhlAId941FYufeh5lZR1htdrFbWKmrHu3Tnjt9ZdhNWfhNAONtTVCJZroXiVpve2orWvEa6+/gdvumCLXa3e6xUIyVuHkOF1uMSTxaFiCUxoWAjcejyIai4gaj/8nN37iiSdh0F+OR0FBIaqranHrrbcJFopLShEMRf444IrN0CfPEF0oO5JGIh7EaYNPQZ8+ffDDDz9i2Xvvi3VlOrW0tEy0tc2iS2Of1bVsjNQlEFGTS1NtZLbMFk24Xfq2N17/N1x4/jkSqNGKcusMCbmvkgMS1EVTmHTd9Xjp5VdRUMitTumBRbVm0pBKxrD0+Wewd58eSKdoYRISICYzaeEaGfXSWt5y6z2YOetB9OnTT4IVq8MhgBMthS4MMSSQdm5/qQgK/G48OGc2Bu7dR9yLVCIOj9OhgzYNk0YrmxLwCg9qtck1b66qxUmnDEYixZ3KjEg0BpvDIQuJ+X0KihT3DVhM5FU11NdUw+mw49+ffgIuGr4RXbJwlEGuA/XBRvi9RQiEwzh20IlobIzC6fCK8eDCDDbV49KLR+FvkyaoMbCakGKKOJOF3eWWz3V7fXjzn+/i0svGwWJ3iuWlZUxyh8pwETJQMCsnN51S+4n45IZGmrpkFajxfmnQREPNghWNSSfqVaxqPKgs/CMSEMaqz/kWuhBcD70QDjXA4bQJm0DWgPrLosJiJJNpoaUUE5FfJm6EZCrwED7XpCJ1ea48n34gfUINVZWVkvW68cbr0bdPbyTi3ArzOV51JekM8MCMOZg95yF4vX5kshThKCE0g45IuAlT77kL5507WICrZZjLJ2OQQZy6BbNVALZg0bO49dY74fEwHZmCzemSwaUFJHDphxopU5vNjMaGLfjTUYdh6r33okNZsVpI6axQbuRgeT8imRTNrC6OF16afGscI0dfiq+++Q4eppfjSXFPNBPFSUoMLoR+JgUzfWarSYDLpMqzzzyFLK2zCLQZSMbF3QjHI7DanXj7nWUYc/E4uFyFsFocIgV12W1oCtRh8k3XYvTI85l+EQcmHo4ofa3dIdQkkxS33HonHl+wCF26dkcwFEUkFidZC41bO29Sd7GyXJCGRkQYFzV/ioNRGVL+WrKiTD9pajdRriLjiPQfA9x8dVS+xE/9TB1uGl6PS1aWEnurh8mkFPgETnO5uAFaw+8T1YC+VEXqJH6uAFejhTGJ7I7b7qRJ1+LSi0cKJ8jVzMCHq59ZG1piZpHe+OcyXDF+ogRotHzUhiqJpQmNDXU47dSTcf/0qXDZleVXS0eJ6+iDcmC/+e4nXH7FBKxfV6GCB04U/8ZJo7hE1/XSmhO469f9iJtu/Buuvkr5zxY9ZU2NMn3TVb/8Irn+ouKiHK0STyXELSBffPfU6Zg5ey7Ky9vL4pPkh/C9KsAlMAlcZFIC3Mb6Glw/6VqMu2wM4rEEXCIrNKCivkfTSdx08+148sklKC3pAGSYLCI/a0NjfRUemjsDJ51wjOyYYhUTCVhsduHVuaBo9U/+62D8+NMqWUi8JrPVBpvDKa4T+V2OB1kB+rpqNI2iU7UYmAjh74VOpVRVBO0MVhmMZ2Vxcyz/MFmjAdyWdanKYhqKfkO2yACKIDb+3xwtGloCw2dUdAn3GMX8cCFwwhVw6SMyn75u7Wqce+7ZmDXjPhkM2fbsNnktwR3V///zqg0YfPqZMJlJ5SifWqmezKirrUZ5u1K8sPRZdOpQIu/D6N9qZRLaCJzMiMXTuPSyy/Hmm2+jvH0nyWrRAjIPz0GmD2nsQtyiN1WswsIF8/DXE49HPJGWjBm3RGabGHFPn3Y/DjroIBx+xOHCz5ILJXAZ4VN48vpb72DEqDEoLi6Bze6S9+BuQZ9UqEjyUByXdEKARm3IkmcXY/+B/cWKOsh1c8cmFZYhdWlDZV0thl44EhWb6Fb4kIymZEtyO22ora7Ac88uwoH77yMpXeUCkWJUGa6GxiZhJU4fchZWfv+jLC5G/qTYNBkDcs3qdeKHy0JTc2dYWwZ7dP9E/5dWrpIBXNIydBs4dwQwhUb/p3pcA9D0U+gm0AlngFZWVio45e+awS57yza6WcPq6ivR0APQumR5s8p6ZdJJyanvvXdfPPbow+hQXiJuCLNH9Pn4zvQNSWYHmsI4/oSTxSqQxxQhOrdAswnhUJO8z5OL5uPoPx8hma1INAKb3QSLySxbuSLd7bjltrsxd+6jaN+hM9IsJtRr4MTHlk80KhvITiTx5KIn0L9vH8QTCqypZAZWuT7gmKMH4YwzzsDYcWPBVC4DUAKM1pvW58fVazD4jLPErfKzDEp0EibY7U6Rb4pWhDFAOiGCmHalhXj5paUoKy5CJp2CTdyKJGBh0iENi82B5R9/jJGjL4HbUyjqt0QsCXMG8LrtqK3aiCXPLMBBB/SHzW5FIpHRNRmq/CcQCAtrMPmWWzDnwYdw8CGHCV9e39Ao7A6TPoVFRRJEEuR2p1cBWYwOAzdm+rLypTbl5mJY3TYp94c7iuxkzBY2m7bf+tNO6TADfPkuQ+5nPS1KqaEhZ+TWS96V//f5vHqhYF6aNC/zxAtPpxlEUfug5BnUNQhwVYJUJoeZHKfDJsDdu29vkcnxXuke8BFPJJBOa6K0+uupp6MxEEIqowZHKffF8UR1VaXQYuOvGAe3ywqWngBJUJqYpM43zaygA4uffh433DAZDqdHIn6xMrnAxCJ5eVW7FcLBB+2NmTOmobioSHYJCUCyAHMaTYEw+vXpi1NP+yvuf+B+mJgKtZgkpcugNZFJIxCM4OJLx+Hjjz9BWbsOklmjhaPlpPMimUa6g9kkIuEAjv/L0Zg+7R4ZDwtvLKVE7BY7U7xAPJvGA7MfxLT7Z6OgsAzZjAXZVBbmTBYFPhfqayowZ+ZUnHLSIGi8jyS5aTOagkEUFfrFXaH7+X9cfsIAACAASURBVNJLr+OysePgdHng9ngVNZhI6oowmzyJklSTlSIbjg8DMi40FWzzmg2lnwq6M8r3yXIhGj6uuvY/FLiyG1BwYhSHySLTZKCF3E6zUFKTDJrQICEq3inyMKpk9fg/L9dPQLAAlsClVdwauOQvuZ0ylWmS7NTTi5/EPv16I5FQ1BWNH+dOth1oAoKh5w8TOstkYQCnVjQpNk50dTUVUPvgkYfnokO5ItmBhKp7S2UliCEX/cMPq3DxxZehuqYBXn+RTCZpIvrUpIqYh6c71NRUjxHDz8Jtt94kC8lOyx6Jw+OyCyW2/P2PcP555wkZP3/BfJS2KxXgknNlajWWSiKdNeH+GbMxc+YcdOjYRcZTM1kZrAtfLYo8WlJTBvFYCJNvnITzzj1T/F6XjelfgoQqN7VRb2mswyWXXY7/fPkd7A6vANeUNcGUzqDA60R9dQWunnAZLrt0hAhkkhlm7KgpZuUxa86UMajYVIu5cx/C0888K74pwUu5JMeBKWbOpsvjRzzN3UqTbBmNDt0ZAW0OuGJaddD+GrjETauA22PAkbnC9pYstkR/usFUAYPS+wiIxd9hCYxfgEv9qdralLPOqD2ZV8zY3NJSgZivT2VYA6UqUCXLRSUZSXfd4qaSccSiYSG533zjdfTq1VNNGov0kqrs26bTS3WNIYwafQm+/+Fn8Re5xfM6hf/0uFFXVy3v+9qrr6DXHj1gNpPYp5Y0Kvdotzpl+4onMhh6/nB8u/InON1Uc2URlSpYikVYkmMV7WpD3RZce/U4XH/dRAm2bBYLamsbUVZSINf10Nx5mD5tmmSVnnzqSezVpzfsFOSEAvB6vELFQbPi+RdexvgJE9Gpc7ecxSXDwOAny6LRdFKuM5uO48mFj2P/Af2EJnTabEgnlRgnlkwiYzbh+19+wRlnnQOTxQHNxAoPDVazDel4HH63U1yFoWefgRuvvxoujwtgqZRZZUbJqTIdzFQvdQo1tU2YMuVurPjgQ6E4S8vaSRDJgJsL12x1ImOyy+6mSp1UUQDpRwVe0pCqeloxC0r22NyIUGk/WgXc7gMP12HZHGnLXehdDrf+IF1MnNefK84ghJMpwuWwgISkP8XJyWRcBl19p7hGkzShCubSSLL3AAeeYgs6/lKbr4r6HKIUsyHNCDwZR011FZ5avEikg1RcEWlUmFnNVF2pzBQt7vARF+Hb736Az1eERFzpLKT4kVkdhx0b1q/BtddMxKRJV0i2jAFWMhVHKh6D1+2RDFIymcX9Mx/Ew48tgKughDYEgWAYxaWlqK2ugt1qkkDHYQHuv+8OHH3UEaitr0dxkaLDWCTMXP6wYRfj008/lXG5++4pOH/o2ZL9stl4/9yp1EL57N9fY/io0RIIeguKEI5xG1KLjpNPKow0WJ89e+Ifr7wIn8uGRExJD8kGiE6WwLABd02diWkzZqJbz16oqWuQAJX6Dsbz8UgEHocNlRUb8Pmn/0LnTuWIxgLw+dwq+ZGkRsEtwS5loe3alQsUPv3sCzz//FK8/fa7qNxSpQpWWTeW1eApKJUdySiQ5PXSj2cARtDSpWIyiAozBmpU5PEhpVrUHmdbaXG77XtYDrhqFzc6m6iVkQ9c9XPzM/h0m0M12YhGwmKJmGKtr6uTCerUqT0SiaiAl4CORsOIhsNCb1E4TcC179xVghpV4qHKvEmf8IsW2Ov1yFZJ8C557hns239vASytjHIuaEN14IZ04H7bDFwGPV6PR7I8BABpsf33G4gF8x+Hx6MU+MqaJYTbpfKK4vTnl76CG265EwlYkYIFmsUq+Xwtm0IqHkU2FUVpoRdLnnoCPbp3QTQah4M1VQzgskBVdRNGjRotugymsUePGoHr/3atpD25o3DXIKdJN2ljRTUuHDESGzZXIku3iVk0kQI6xN1iurexrkaYixn33QMPBeOs2pXpUi5TmunzaApjr5yAZR98AH9RMaJ64xH6lhwru8WKTCKBzRs34uYbbsDVE8eITxqLBYU65LUozbRqthwW2ktVSPNBTe8bb/xTFiPLdMjcbKqsEfqRuy7BTH0IGSUaDMpPGcwxm8rgnfdDd4M7l0hkhTqiFsSIgX5raAZo3fY1LC4DAMPqNndpaAauDthcyx5JZsLtcaOmphouh0P82ZiewSH/+tO3X8Nd4EPHjuXotcce6Na1M8pKS8QPJiAdLie21NZLjpwAo5/FLai+tl4EORTh8H3qamsQi0Ww5NmncfJJx+eUWUbWzAAuhdpicb/9AV7d4pJzZPWAUvLTotgFvC88vwQDBvRCMMosF33SDKLhoFgmu8uLn35agwtGXozqhpAA1+n1iUViZUMqFkagvhqHHjgQLzz7pOwkQkllNFG62ewaPvjwS1xxxfhcM5LevXpIcNmpYymSoj22IBaNCdgNwc27y1eItfUVFiMUicLpcuWAGwkGcNvkG3HukMFw2ZiYUJNNV4GBktkGfPL5Sgy/aAwy9OvJKYv+gdSiSXY8FxcEiyAjERR6vXho7kwM2Lc3otGQzAmrq6OxqBgRVdiYFV+cSQmC0eulpsIkut0Vy1fggw8/xqq16/H9Dz9h06ZN8hoWbzLmYAKFC1jVurFU3qPulcWoYcUDSwV2NLyrwFV+SM6e5lwFg8UyrG+zxeVSDwQa0aVzR7mgcCgowVIw2IQO7dvhpJNOwBGHH4Z2ZSXo1KkjCgtcubolMgPSMJHi6W0WWjCYkBVdV1uHNaspztmA9evXYtzYS9G/f18JBGgZFL1kBz1iXlWAwB3e7CpQf8Ctktk2ThzV0R07lAsvPPmmG3DRRUMRoS9p51ZK4IZEqG1jTj6RxfnDL8IX3/0EC0UrJuVyZJLUS1hQXVmBK8aOwa03Xic+OH3qaCxBGTWcLgseeXQx7ppyt0wk3RQuFma7jjj8QIRDYXg8TiSp+Eprknl85LEnMX3GTGSppHJ5EZJKXWYQGWNk4LCYsGTxIuy1Rw84JMuRpXQOmQyTIhoYi859ZBEm3TQZXXv0RJRdeixWyahRl8wxIG1i1TQU+nz49quvMPi0k/HArHuFTaCr0BRgqVWJzAZZBtKbkZDSS3OhyUKhW049hh760Kv5zxdfYcWKFfjyq6+wbt16EfRLCtnuENEO4ySm1Sm/5KulbRM0ybRSX9EqH1dZ3K1dAEWpKjAY2Tz1s26RdYDTEaevR36UPgy3RPo4Z545BMMuPB8lxUVwOdlTjNG9WgCiRcjqvcXYY8uUFatAQQw/g6lXFkuoT9d9agDV1Y1oV6aCHq5+BhEqqaiawbfk4xK4LItuqK+XJATrzOh7E6AHHrAf5i94SD5A7krq58RsSvWDxe7A7VPux6Pzn4K/pB2C4ZgUTm7ZtAHlZUVoqq/Bww/OwCknDBLxis9fKMBl8sNqA8ZePkmUcSxNpwu1bs0qPDhnFs4fOgTRSFTq56i7YOBHPvX9FZ9i9JiLYXN5JJ1KALOokQmLRCyCPffogZeWLhH/lrglcCkuN5ntIhekfOCiSybghVdeRftOnZE1U5ObkvGxszgxnUImmYLNbILP7UbFhvUih7zksosw7vLLUODzoK4hIOlpP4O2/Ab3WcWq8HrpuwqHTr+ZYnwWCyhvRWKGNWs34LN/f4633npbFGW01rS81DfQ8mrSX07pjqVtgTHZv91LkGdq3fY9Qhm8vK59za0ntwWuevdmy8yMTkzkeyyBpg7zmmuuwdF/PkjAEI2m4aL8SOehecEqw6I3EMlmYKWqSDfqQjlxFiTVyTQh/Vg1MNyKucqTSZFbiwVQmXFdM5sXnH2jB2cELoM+BghkFRjocZExG8f3eu21V9CxQ4mwE6ZMWqoLGH0bwH3tjfcw7KKxKC7vKMFZhw4dsLliHYr8bvjdDjy96An07N5Z1G12B9PMaqU1BuI4bfAZ2LS5UnWscTlRW1OFSy4eLToBjo1RiyaZIwCr1lbgzLPOQVMoCjPbGDmpmuNOkZaFduYZp2H6fXeAbFWGCRiLSQI0VhDwDZk1HD3mEmyurhGVl9PtRZPet4yWjZkrF+mvWFQF0E4nItGwxAunDT4N46+4HFT6cYqi0aTUnvE6A40hoSSpDxHWJ63aSRG8nDtjC+W80cparGoHraoO4NvvVuKJ+Qvxw48/oaa2HoWFxap7Tygs7ghjBoMZ+p24hdZtIOmwPBchD5jqt/ojrxtkc5BG1WcciVgYQ4YMwfXXXw+fz45AgFUBcRQWFMgWxbQrK0MNFT1BKN0QU9wqEzBbLbKtWJih0ik/rl4OEoMZtQiYqzfLKjce5IrdXq9k3MVVCEYwTGcV6OMSuLTkqhEcn6H8WHK6DQ11mDljOs444xQlmsmkBLgJ9kGgRiGrYe2GKgw6eTBMLHQMR1BYWIBUIoJ4uAlHH3UoHntoDkyaqsaVZIpoDYBvvv0ZJ5x4Cjp07CTboUQDmaSIhB6f9yiKCj0INgVkN+BWxAkIhhO4bOzleH/FR6oWS09Zi9Y1HsUdt92Mc886VVRiMfq/TjsyzBCmTLA5TJj3xDO4Z+p9cHq8CMficHl8CLMiWHzUuASnTrsNsUhEgkMaBZaqR+IxhCNhHHzQgRg5cqS4MnZ9xws0RlBU4EIyweQBOXU1Pwz2jCYwsURMwMjRpeFhvGKVBniq5Ilfr7+********************************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
