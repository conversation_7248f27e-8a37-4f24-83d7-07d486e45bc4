app_version: 1.0.0
name: NLP
description: An NLP app to classify text
contact_info:
  name: "@0xpookie"
  url: 'localhost'
  email: <EMAIL>
tags:
  - Parser
categories:
  - Parser 
actions:
  - name: extract
    description: Runs nlp extract on input data
    parameters:
      - name: data
        description: The text to handle 
        required: true
        multiline: true
        example: ''
        schema:
          type: string
      - name: extract
        description: The selected nlp extract to run
        required: true
        multiline: true
        example: 'get_entities'
        options: 
          - get_ipv4s
          - get_urls
          - get_emails
          - get_entities
          - get_content
        schema:
          type: string
    returns:
      schema:
        type: list
large_image: data:image/png;base64,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
