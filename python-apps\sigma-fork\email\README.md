# Mail
This apps is used to interact with SMTP, IMAP and contains other basic email tools

## eml parsing
You can parse eml files by running the "Parse email file" action

### Using curl to test whether you SMPT mails are getting delivered.
``` 
curl --ssl smtp://1.1.1.1:587 --mail-from <EMAIL> --mail-rcpt <EMAIL> --upload-file email.txt --user 'example:Password123' -k -v
```

### email.txt : Should contain the below
```
From: <<EMAIL>>
To: <<EMAIL>>
Subject: an example.com example email
Date: Thu, 24 Mar 2022 11:29:16

Welcome to this example email. What a lovely day
```
