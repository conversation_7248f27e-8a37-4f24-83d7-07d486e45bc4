name: Crowdstrike Falcon
is_valid: true
id: ""
link: https://api.crowdstrike.com
app_version: 1.0.0
sharing_config: ""
generated: true
downloaded: false
sharing: false
verified: false
invalid: false
activated: true
tested: false
hash: ""
private_id: ""
description:  Each API endpoint requires authorization via an OAuth2 token. Your first API request
  should retrieve an OAuth2 token using the `oauth2/token` endpoint, such as `https://api.crowdstrike.com/oauth2/token`. Any action should be preceeded by a `get oauth2 access token` action titled `auth` that feeds the access token into it. Tokens expire after 30 minutes, after which you should make a new token request
  to continue making API requests.
environment: Shuffle
contact_info:
  name: "test"
  url: "test"
referenceinfo:
  documentationurl: ""
  githuburl: ""
foldermount:
  foldermount: false
  sourcefolder: ""
  destinationfolder: ""
actions:
- description: ""
  name: generate_oauth2_access_token
  label: OAuth2 - Generate an OAuth2 access token
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Accept: application/json
      Content-Type: application/x-www-form-urlencoded
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_detect_aggregates
  label: Detects - Get detect aggregates
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: view_information_about_detections
  label: Detects - View information about detections
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "ids": "${ids}"
      }
    value: |-
      {
        "ids": "${ids}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: modify_detections
  label: Detects - Modify the state assignee and visibility of detections
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "assigned_to_uuid": "${assigned_to_uuid}",
        "comment": "${comment}",
        "ids": "${ids}",
        "show_in_ui": "${show_in_ui}",
        "status": "${status}"
      }
    value: |-
      {
        "assigned_to_uuid": "${assigned_to_uuid}",
        "comment": "${comment}",
        "ids": "${ids}",
        "show_in_ui": "${show_in_ui}",
        "status": "${status}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_detection_ids
  label: Detects - Search for detection IDs that match a given query
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The first detection to return, where `0` is the latest detection.
      Use with the `limit` parameter to manage pagination of results.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'The maximum number of detections to return in this response (default:
      9999; max: 9999). Use with the `offset` parameter to manage pagination of results.'
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Sort detections using these options:

      - `first_behavior`: Timestamp of the first behavior associated with this detection
      - `last_behavior`: Timestamp of the last behavior associated with this detection
      - `max_severity`: Highest severity of the behaviors associated with this detection
      - `max_confidence`: Highest confidence of the behaviors associated with this detection
      - `adversary_id`: ID of the adversary associated with this detection, if any
      - `devices.hostname`: Hostname of the host where this detection was detected

      Sort either `asc` (ascending) or `desc` (descending). For example: `last_behavior|asc`
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: "Filter detections using a query in Falcon Query Language (FQL) An
      asterisk wildcard `*` includes all results. \n\nCommon filter options include:\n\n-
      `status`\n- `device.device_id`\n- `max_severity`\n\nThe full list of valid filter
      options is extensive. Review it in our [documentation inside the Falcon console](https://falcon.crowdstrike.com/support/documentation/2/query-api-reference#detections_fql)."
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Search all detection metadata for the provided string
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_set_of_host_groups
  label: Host Group - Retrieve a set of Host Groups by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Host Groups to return
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_set_of_host_groups
  label: Host Group - Delete a set of Host Groups by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Host Groups to delete
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_host_groups
  label: Host Group - Create Host Groups by specifying details about the group to create
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_host_groups
  label: Host Group - Update Host Groups by specifying the ID of the group and details to update
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_host_groups
  label: Host Group - Search for Host Groups in your environment by providing an FQL filter and
    paging details Returns a set of Host Groups which match the filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_host_group_ids
  label: Host Group - Search for Host Groups in your environment by providing an FQL filter and
    paging details Returns a set of Host Group IDs which match the filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_host_group_members
  label: Host Group - Search for members of a Host Group in your environment by providing an FQL
    filter and paging details Returns a set of host details which match the filter
    criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Host Group to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: perform_action_on_host_group
  label: Host Group - Perform the specified action on the Host Groups specified in the request
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The action to perform
    name: action_name
    example: ""
    multiline: false
    options:
      - add-hosts
      - remove-hosts
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the host group to change
    name: host_group_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The hostnames to change
    name: hostnames
    example: ""
    multiline: true
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_host_group_member_ids
  label: Host Group - Search for members of a Host Group in your environment by providing an FQL
    filter and paging details Returns a set of Agent IDs which match the filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Host Group to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_hidden_hosts
  label: Hosts - Retrieve hidden hosts that match the provided filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by (e.g. status.desc or hostname.asc)
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_hosts
  label: Hosts - Search for hosts in your environment by platform hostname IP and other criteria
    with continuous pagination capability based on offset pointer which expires after
    2 minutes with no maximum limit
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to page from, for the next result set
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by (e.g. status.desc or hostname.asc)
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: modify_host_tags
  label: Hosts - Append or remove one or more Falcon Grouping Tags on one or more hosts
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "action": "${action}",
        "device_ids": "${device_ids}",
        "tags": "${tags}"
      }
    value: |-
      {
        "action": "${action}",
        "device_ids": "${device_ids}",
        "tags": "${tags}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_details_on_hosts
  label: Hosts - Get details on one or more hosts by providing agent IDs AID You can get a
    hosts agent IDs AIDs from the devicesqueriesdevicesv1 endpoint the Falcon console
    or the Streaming API
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The host agentIDs used to get details on
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: take_action_on_hosts
  label: Hosts - Take various actions on the hosts in your environment Contain or lift containment
    on a host Delete or restore a host
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: |-
      Specify one of these actions:

      - `contain` - This action contains the host, which stops any network communications to locations other than the CrowdStrike cloud and IPs specified in your [containment policy](https://falcon.crowdstrike.com/support/documentation/11/getting-started-guide#containmentpolicy)
      - `lift_containment`: This action lifts containment on the host, which returns its network communications to normal
      - `hide_host`: This action will delete a host. After the host is deleted, no new detections for that host will be reported via UI or APIs
      - `unhide_host`: This action will restore a host. Detection reporting will resume after the host is restored
    name: action_name
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "action_parameters": "${action_parameters}",
        "ids": "${ids}"
      }
    value: |-
      {
        "action_parameters": "${action_parameters}",
        "ids": "${ids}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_hosts
  label: Hosts - Search for hosts in your environment by platform hostname IP and other criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by (e.g. status.desc or hostname.asc)
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: download_analysis_artifacts
  label: FalconX Sandbox - Download IOC packs PCAP files and other analysis artifacts
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: ID of an artifact, such as an IOC pack, PCAP file, or actor image.
      Find an artifact ID in a report or summary.
    name: id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: gzip
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The name given to your downloaded file.
    name: name
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_sandbox_reports
  label: FalconX Sandbox - Find sandbox reports by providing an FQL filter and paging details Returns
    a set of report IDs that match your criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Optional filter and sort criteria in the form of an FQL query. For
      more information about FQL queries, see [our FQL documentation in Falcon](https://falcon.crowdstrike.com/support/documentation/45/falcon-query-language-feature-guide).
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving reports from.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Maximum number of report IDs to return. Max: 5000.'
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Sort order: `asc` or `desc`.'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_full_sandbox_report
  label: FalconX Sandbox - Get a full sandbox report
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: ID of a report. Find a report ID from the response when submitting
      a malware sample or search with `/falconx/queries/reports/v1`.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_report
  label: FalconX Sandbox - Delete report based on the report ID Operation can be checked for success
    by polling for the report ID on the reportsummaries endpoint
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: ID of a report.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_list_of_samples
  label: FalconX Sandbox - retrieve a list with sha256 of samples that exist and customer has rights
    to access them maximum number of accepted items is 200
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "sha256s": "${sha256s}"
      }
    value: |-
      {
        "sha256s": "${sha256s}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: check_status_of_sandbox_analysis
  label: FalconX Sandbox - Check the status of a sandbox analysis Time required for analysis varies
    but is usually less than 15 minutes
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: ID of a submitted malware sample. Find a submission ID from the response
      when submitting a malware sample or search with `/falconx/queries/submissions/v1`.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: submit_upload_for_sandbox_analysis
  label: FalconX Sandbox - Submit an uploaded file or a URL for sandbox analysis Time required for analysis
    varies but is usually less than 15 minutes
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_short_summary_version_of_a_sandbox_report
  label: FalconX Sandbox - Get a short summary version of a sandbox report
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: ID of a summary. Find a summary ID from the response when submitting
      a malware sample or search with `/falconx/queries/reports/v1`.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: find_submission_ids_for_uploaded_files
  label: FalconX Sandbox - Find submission IDs for uploaded files by providing an FQL filter and paging
    details Returns a set of submission IDs that match your criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Optional filter and sort criteria in the form of an FQL query. For
      more information about FQL queries, see [our FQL documentation in Falcon](https://falcon.crowdstrike.com/support/documentation/45/falcon-query-language-feature-guide).
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving submissions from.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Maximum number of submission IDs to return. Max: 5000.'
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Sort order: `asc` or `desc`.'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_the_file_associated_with_the_given_id_sha256
  label: FalconX Sandbox - retrieve the file associated with the given ID SHA256
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The file SHA256.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Flag whether the sample should be zipped and password protected with
      pass='infected'
    name: password_protected
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_sample_from_the_collection
  label: FalconX Sandbox - Removes a sample including file meta and submissions from the collection
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The file SHA256.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: upload_for_sandbox_analysis
  label: FalconX Sandbox - Upload a file for sandbox analysis After uploading use falconxentitiessubmissionsv1
    to start analyzing the file
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Name of the file.
    name: file_name
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: A descriptive comment to identify the file for other users.
    name: comment
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: "Defines visibility of this file in Falcon MalQuery, either via the
      API or the Falcon console.\n\n- `true`: File is only shown to users within your
      customer account\n- `false`: File can be seen by other CrowdStrike customers
      \n\nDefault: `true`."
    name: is_confidential
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_behaviors
  label: Incidents - Search for behaviors by providing an FQL filter sorting and paging details
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Optional filter and sort criteria in the form of an FQL query. For
      more information about FQL queries, see [our FQL documentation in Falcon](https://falcon.crowdstrike.com/support/documentation/45/falcon-query-language-feature-guide).
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return ids.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-500]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort on, followed by a dot (.), followed by the sort
      direction, either "asc" or "desc".
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_incidents
  label: Incidents - Search for incidents by providing an FQL filter sorting and paging details
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort on, followed by a dot (.), followed by the sort
      direction, either "asc" or "desc".
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Optional filter and sort criteria in the form of an FQL query. For
      more information about FQL queries, see [our FQL documentation in Falcon](https://falcon.crowdstrike.com/support/documentation/45/falcon-query-language-feature-guide).
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return ids.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-500]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: query_crowdscore
  label: Incidents - Query environment wide CrowdScore and return the entity data
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Optional filter and sort criteria in the form of an FQL query. For
      more information about FQL queries, see [our FQL documentation in Falcon](https://falcon.crowdstrike.com/support/documentation/45/falcon-query-language-feature-guide).
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return ids.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-2500]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort on, followed by a dot (.), followed by the sort
      direction, either "asc" or "desc".
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: perform_actions_on_incidents
  label: Incidents - Perform a set of actions on one or more incidents such as adding tags or
    comments or updating the incident name or description
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "action_parameters": "${action_parameters}",
        "ids": "${ids}"
      }
    value: |-
      {
        "action_parameters": "${action_parameters}",
        "ids": "${ids}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_details_on_behaviors
  label: Incidents - Get details on behaviors by providing behavior IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "ids": "${ids}"
      }
    value: |-
      {
        "ids": "${ids}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_details_on_incidents
  label: Incidents - Get details on incidents by providing incident IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "ids": "${ids}"
      }
    value: |-
      {
        "ids": "${ids}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_combined_for_indicators
  label: IOCs - Get Combined for Indicators
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from. Offset and After params
      are mutually exclusive. If none provided then scrolling will be used by default.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The sort expression that should be used to sort the results.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_indicators_by_ids
  label: IOCs - Get Indicators by ids
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The ids of the Indicators to retrieve
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_indicators_by_ids
  label: IOCs - Delete Indicators by ids
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The FQL expression to delete Indicators in bulk. If both 'filter'
      and 'ids' are provided, then filter takes precedence and ignores ids.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ids of the Indicators to delete. If both 'filter' and 'ids' are
      provided, then filter takes precedence and ignores ids
    name: ids
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The comment why these indicators were deleted
    name: comment
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_indicators
  label: IOCs - Create Indicators
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Whether to submit to retrodetects
    name: retrodetects
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set to true to ignore warnings and add all IOCs
    name: ignore_warnings
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "comment": "${comment}",
        "indicators": "${indicators}"
      }
    value: |-
      {
        "comment": "${comment}",
        "indicators": "${indicators}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_indicators
  label: IOCs - Update Indicators
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Whether to submit to retrodetects
    name: retrodetects
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set to true to ignore warnings and add all IOCs
    name: ignore_warnings
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "bulk_update": "${bulk_update}",
        "comment": "${comment}",
        "indicators": "${indicators}"
      }
    value: |-
      {
        "bulk_update": "${bulk_update}",
        "comment": "${comment}",
        "indicators": "${indicators}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_number_of_hosts_that_have_observed_a_given_custom_ioc
  label: IOCs - Number of hosts in your customer account that have observed a given custom
    IOC
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: |2

      The type of the indicator. Valid types include:

      sha256: A hex-encoded sha256 hash string. Length - min: 64, max: 64.

      md5: A hex-encoded md5 hash string. Length - min 32, max: 32.

      domain: A domain name. Length - min: 1, max: 200.

      ipv4: An IPv4 address. Must be a valid IP address.

      ipv6: An IPv6 address. Must be a valid IP address.
    name: type
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The string representation of the indicator
    name: value
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_process_details
  label: IOCs - For the provided ProcessID retrieve the process details
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: ProcessID for the running process you want to lookup
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_hosts_that_have_observed_a_given_custom_ioc
  label: IOCs - Find hosts that have observed a given custom IOC For details about those
    hosts use GET devicesentitiesdevicesv1
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: |2

      The type of the indicator. Valid types include:

      sha256: A hex-encoded sha256 hash string. Length - min: 64, max: 64.

      md5: A hex-encoded md5 hash string. Length - min 32, max: 32.

      domain: A domain name. Length - min: 1, max: 200.

      ipv4: An IPv4 address. Must be a valid IP address.

      ipv6: An IPv6 address. Must be a valid IP address.
    name: type
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The string representation of the indicator
    name: value
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The first process to return, where 0 is the latest offset. Use with
      the offset parameter to manage pagination of results.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The first process to return, where 0 is the latest offset. Use with
      the limit parameter to manage pagination of results.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_processes_associated_with_a_custom_ioc
  label: IOCs - Search for processes associated with a custom IOC
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: |2

      The type of the indicator. Valid types include:

      sha256: A hex-encoded sha256 hash string. Length - min: 64, max: 64.

      md5: A hex-encoded md5 hash string. Length - min 32, max: 32.

      domain: A domain name. Length - min: 1, max: 200.

      ipv4: An IPv4 address. Must be a valid IP address.

      ipv6: An IPv6 address. Must be a valid IP address.
    name: type
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The string representation of the indicator
    name: value
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Specify a host's ID to return only processes from that host. Get
      a host's ID from GET /devices/queries/devices/v1, the Falcon console, or the
      Streaming API.
    name: device_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The first process to return, where 0 is the latest offset. Use with
      the offset parameter to manage pagination of results.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The first process to return, where 0 is the latest offset. Use with
      the limit parameter to manage pagination of results.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_indicators
  label: IOCs - Search for Indicators
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from. Offset and After params
      are mutually exclusive. If none provided then scrolling will be used by default.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The sort expression that should be used to sort the results.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_info_about_indicators
  label: Intel - Get info about indicators that match provided FQL filters
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the starting row number to return indicators from. Defaults to
      0.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the number of indicators to return. The number must be between
      1 and 50000
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Order fields in ascending or descending order.

      Ex: published_date|asc.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Filter your query by specifying FQL filter parameters. Filter parameters include:

      _marker, actors, deleted, domain_types, id, indicator, ip_address_types, kill_chains, labels, labels.created_on, labels.last_valid_on, labels.name, last_updated, malicious_confidence, malware_families, published_date, reports, targets, threat_types, type, vulnerabilities.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Perform a generic substring search across all fields.
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: If true, include both published and deleted indicators in the response.
      Defaults to false.
    name: include_deleted
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: download_earlier_rule_sets
  label: Intel - Download earlier rule sets
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The ID of the rule set.
    name: id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Choose the format you want the rule set in. Valid formats are zip
      and gzip. Defaults to zip.
    name: format
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_report_ids
  label: Intel - Get report IDs that match provided FQL filters
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the starting row number to return report IDs from. Defaults to
      0.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the number of report IDs to return. The value must be between
      1 and 5000.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Order fields in ascending or descending order.

      Ex: created_date|asc.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Filter your query by specifying FQL filter parameters. Filter parameters include:

      actors, actors.id, actors.name, actors.slug, actors.url, created_date, description, id, last_modified_date, motivations, motivations.id, motivations.slug, motivations.value, name, name.raw, short_description, slug, sub_type, sub_type.id, sub_type.name, sub_type.slug, tags, tags.id, tags.slug, tags.value, target_countries, target_countries.id, target_countries.slug, target_countries.value, target_industries, target_industries.id, target_industries.slug, target_industries.value, type, type.id, type.name, type.slug, url.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Perform a generic substring search across all fields.
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_rule_ids
  label: Intel - Search for rule IDs that match provided filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: |-
      The rule news report type. Accepted values:

      snort-suricata-master

      snort-suricata-update

      snort-suricata-changelog

      yara-master

      yara-update

      yara-changelog

      common-event-format

      netwitness
    name: type
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the starting row number to return reports from. Defaults to 0.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The number of rule IDs to return. Defaults to 10.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Order fields in ascending or descending order.

      Ex: created_date|asc.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Search by rule title.
    name: name
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Substring match on description field.
    name: description
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Search for rule tags.
    name: tags
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Filter results to those created on or after a certain date.
    name: min_created_date
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Filter results to those created on or before a certain date.
    name: max_created_date
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Perform a generic substring search across all fields.
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_info_about_reports
  label: Intel - Get info about reports that match provided FQL filters
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the starting row number to return reports from. Defaults to 0.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the number of reports to return. The value must be between 1
      and 5000.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Order fields in ascending or descending order. Ex: created_date|asc.'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Filter your query by specifying FQL filter parameters. Filter parameters include:

      actors, actors.id, actors.name, actors.slug, actors.url, created_date, description, id, last_modified_date, motivations, motivations.id, motivations.slug, motivations.value, name, name.raw, short_description, slug, sub_type, sub_type.id, sub_type.name, sub_type.slug, tags, tags.id, tags.slug, tags.value, target_countries, target_countries.id, target_countries.slug, target_countries.value, target_industries, target_industries.id, target_industries.slug, target_industries.value, type, type.id, type.name, type.slug, url.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Perform a generic substring search across all fields.
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      The fields to return, or a predefined set of fields in the form of the collection name surrounded by two underscores like:

      \_\_\<collection\>\_\_.

      Ex: slug \_\_full\_\_.

      Defaults to \_\_basic\_\_.
    name: fields
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_indicators_ids
  label: Intel - Get indicators IDs that match provided FQL filters
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the starting row number to return indicator IDs from. Defaults
      to 0.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the number of indicator IDs to return. The number must be between
      1 and 50000
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Order fields in ascending or descending order.

      Ex: published_date|asc.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Filter your query by specifying FQL filter parameters. Filter parameters include:

      _marker, actors, deleted, domain_types, id, indicator, ip_address_types, kill_chains, labels, labels.created_on, labels.last_valid_on, labels.name, last_updated, malicious_confidence, malware_families, published_date, reports, targets, threat_types, type, vulnerabilities.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Perform a generic substring search across all fields.
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: If true, include both published and deleted indicators in the response.
      Defaults to false.
    name: include_deleted
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_specific_actors_using_their_actor_ids
  label: Intel - Retrieve specific actors using their actor IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "ids": "${ids}"
      }
    value: |-
      {
        "ids": "${ids}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_specific_indicators_using_their_indicator_ids
  label: Intel - Retrieve specific indicators using their indicator IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "ids": "${ids}"
      }
    value: |-
      {
        "ids": "${ids}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_info_about_actors
  label: Intel - Get info about actors that match provided FQL filters
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the starting row number to return actors from. Defaults to 0.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the number of actors to return. The value must be between 1 and
      5000.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Order fields in ascending or descending order.

      Ex: created_date|asc.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Filter your query by specifying FQL filter parameters. Filter parameters include:

      actors, actors.id, actors.name, actors.slug, actors.url, created_date, description, id, last_modified_date, motivations, motivations.id, motivations.slug, motivations.value, name, name.raw, short_description, slug, sub_type, sub_type.id, sub_type.name, sub_type.slug, tags, tags.id, tags.slug, tags.value, target_countries, target_countries.id, target_countries.slug, target_countries.value, target_industries, target_industries.id, target_industries.slug, target_industries.value, type, type.id, type.name, type.slug, url.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Perform a generic substring search across all fields.
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      The fields to return, or a predefined set of fields in the form of the collection name surrounded by two underscores like:

      \_\_\<collection\>\_\_.

      Ex: slug \_\_full\_\_.

      Defaults to \_\_basic\_\_.
    name: fields
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_report_pdf_attachment
  label: Intel - Return a Report PDF attachment
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The ID of the report you want to download as a PDF.
    name: id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: download_the_latest_rule_set
  label: Intel - Download the latest rule set
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: |-
      The rule news report type. Accepted values:

      snort-suricata-master

      snort-suricata-update

      snort-suricata-changelog

      yara-master

      yara-update

      yara-changelog

      common-event-format

      netwitness
    name: type
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Choose the format you want the rule set in. Valid formats are zip
      and gzip. Defaults to zip.
    name: format
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_details_for_rule_sets_for_ids
  label: Intel - Retrieve details for rule sets for the specified ids
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The ids of rules to return.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_actor_ids
  label: Intel - Get actor IDs that match provided FQL filters
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the starting row number to return actors IDs from. Defaults to
      0.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Set the number of actor IDs to return. The value must be between
      1 and 5000.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Order fields in ascending or descending order.

      Ex: created_date|asc.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Filter your query by specifying FQL filter parameters. Filter parameters include:

      actors, actors.id, actors.name, actors.slug, actors.url, created_date, description, id, last_modified_date, motivations, motivations.id, motivations.slug, motivations.value, name, name.raw, short_description, slug, sub_type, sub_type.id, sub_type.name, sub_type.slug, tags, tags.id, tags.slug, tags.value, target_countries, target_countries.id, target_countries.slug, target_countries.value, target_industries, target_industries.id, target_industries.slug, target_industries.value, type, type.id, type.name, type.slug, url.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Perform a generic substring search across all fields.
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_specific_reports_using_their_report_ids
  label: Intel - Retrieve specific reports using their report IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the reports you want to retrieve.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      The fields to return, or a predefined set of fields in the form of the collection name surrounded by two underscores like:

      \_\_\<collection\>\_\_.

      Ex: slug \_\_full\_\_.

      Defaults to \_\_basic\_\_.
    name: fields
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_rules_by_id
  label: Custom IOA - Get rules by ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the entities
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_rules_from_a_rule_group_by_id
  label: Custom IOA - Delete rules from a rule group by ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The parent rule group
    name: rule_group_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The IDs of the entities
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Explains why the entity is being deleted
    name: comment
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_a_rule_within_a_rule_group
  label: Custom IOA - Create a rule within a rule group
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "comment": "${comment}",
        "description": "${description}",
        "disposition_id": "${disposition_id}",
        "field_values": "${field_values}",
        "name": "${name}",
        "pattern_severity": "${pattern_severity}",
        "rulegroup_id": "${rulegroup_id}",
        "ruletype_id": "${ruletype_id}"
      }
    value: |-
      {
        "comment": "${comment}",
        "description": "${description}",
        "disposition_id": "${disposition_id}",
        "field_values": "${field_values}",
        "name": "${name}",
        "pattern_severity": "${pattern_severity}",
        "rulegroup_id": "${rulegroup_id}",
        "ruletype_id": "${ruletype_id}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_rules_within_a_rule_group
  label: Custom IOA - Update rules within a rule group
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "comment": "${comment}",
        "rule_updates": "${rule_updates}",
        "rulegroup_id": "${rulegroup_id}",
        "rulegroup_version": "${rulegroup_version}"
      }
    value: |-
      {
        "comment": "${comment}",
        "rule_updates": "${rule_updates}",
        "rulegroup_id": "${rulegroup_id}",
        "rulegroup_version": "${rulegroup_version}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_rule_types_by_id
  label: Custom IOA - Get rule types by ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the entities
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_all_platform_ids
  label: Custom IOA - Get all platform IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return IDs
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of IDs to return
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: find_all_rule_ids
  label: Custom IOA - Finds all rule IDs matching the query with optional filter
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Possible order by fields: {rules.ruletype_name, rules.enabled, rules.created_by,
      rules.current_version.name, rules.current_version.modified_by, rules.created_on,
      rules.current_version.description, rules.current_version.pattern_severity, rules.current_version.action_label,
      rules.current_version.modified_on}'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'FQL query specifying the filter parameters. Filter term criteria:
      [enabled platform name description rules.action_label rules.name rules.description
      rules.pattern_severity rules.ruletype_name rules.enabled]. Filter range criteria:
      created_on, modified_on; use any common date format, such as ''2010-05-15T14:55:21.892315096Z''.'
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Match query criteria, which includes all the filter string fields
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return IDs
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of IDs to return
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: find_all_rule_group_ids
  label: Custom IOA - Finds all rule group IDs matching the query with optional filter
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Possible order by fields: {created_by, created_on, modified_by,
      modified_on, enabled, name, description}'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'FQL query specifying the filter parameters. Filter term criteria:
      [enabled platform name description rules.action_label rules.name rules.description
      rules.pattern_severity rules.ruletype_name rules.enabled]. Filter range criteria:
      created_on, modified_on; use any common date format, such as ''2010-05-15T14:55:21.892315096Z''.'
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Match query criteria, which includes all the filter string fields
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return IDs
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of IDs to return
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_rule_groups_by_id
  label: Custom IOA - Get rule groups by ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the entities
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_rule_groups_by_id
  label: Custom IOA - Delete rule groups by ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the entities
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Explains why the entity is being deleted
    name: comment
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_a_rule_group
  label: Custom IOA - Create a rule group for a platform with a name and an optional description
    Returns the rule group
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "comment": "${comment}",
        "description": "${description}",
        "name": "${name}",
        "platform": "${platform}"
      }
    value: |-
      {
        "comment": "${comment}",
        "description": "${description}",
        "name": "${name}",
        "platform": "${platform}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_a_rule_group
  label: Custom IOA - Update a rule group The following properties can be modified name description
    enabled
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "comment": "${comment}",
        "description": "${description}",
        "enabled": "${enabled}",
        "id": "${id}",
        "name": "${name}",
        "rulegroup_version": "${rulegroup_version}"
      }
    value: |-
      {
        "comment": "${comment}",
        "description": "${description}",
        "enabled": "${enabled}",
        "id": "${id}",
        "name": "${name}",
        "rulegroup_version": "${rulegroup_version}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_all_rule_type_ids
  label: Custom IOA - Get all rule type IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return IDs
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of IDs to return
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_all_pattern_severity_ids
  label: Custom IOA - Get all pattern severity IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return IDs
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of IDs to return
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: validates_field_values_and_checks_for_string_matches
  label: Custom IOA - Validates field values and checks for matches if a test string is provided
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "fields": "${fields}"
      }
    value: |-
      {
        "fields": "${fields}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_rules_by_id
  label: Custom IOA - Get rules by ID and optionally version in the following format IDversion
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "ids": "${ids}"
      }
    value: |-
      {
        "ids": "${ids}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: find_all_rule_groups
  label: Custom IOA - Find all rule groups matching the query with optional filter
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Possible order by fields: {created_by, created_on, modified_by,
      modified_on, enabled, name, description}'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'FQL query specifying the filter parameters. Filter term criteria:
      [enabled platform name description rules.action_label rules.name rules.description
      rules.pattern_severity rules.ruletype_name rules.enabled]. Filter range criteria:
      created_on, modified_on; use any common date format, such as ''2010-05-15T14:55:21.892315096Z''.'
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Match query criteria, which includes all the filter string fields
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return IDs
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of IDs to return
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_platforms_by_id
  label: Custom IOA - Get platforms by ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the entities
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_pattern_severities_by_id
  label: Custom IOA - Get pattern severities by ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the entities
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_zipped_sample
  label: Malquery - Fetch a zip archive with password infected containing the samples Call this
    once the entitiessamplesmultidownload request has finished processing
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Multidownload job id
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: schedule_samples_for_download
  label: Malquery - Schedule samples for download Use the result id with the request endpoint
    to check if the download is ready after which you can call the entitiessamplesfetch
    to get the zip
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "samples": "${samples}"
      }
    value: |-
      {
        "samples": "${samples}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_falcon_malquery
  label: Malquery - Search Falcon MalQuery for a combination of hex patterns and strings in order
    to identify samples based upon file content at byte level granularity You can
    filter results on criteria such as file type file size and first seen date Returns
    a request id which can be used with the request endpoint
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "options": "${options}",
        "patterns": "${patterns}"
      }
    value: |-
      {
        "options": "${options}",
        "patterns": "${patterns}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_information_about_search_and_download_quotas
  label: Malquery - Get information about search and download quotas in your environment
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_indexed_files_metadata_by_their_hash
  label: Malquery - Retrieve indexed files metadata by their hash
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The file SHA256.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: schedule_a_yara_based_search_for_execution
  label: Malquery - Schedule a YARAbased search for execution Returns a request id which can
    be used with the request endpoint
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "options": "${options}",
        "yara_rule": "${yara_rule}"
      }
    value: |-
      {
        "options": "${options}",
        "yara_rule": "${yara_rule}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: check_the_status_and_results_of_an_asynchronous_request
  label: Malquery - Check the status and results of an asynchronous request such as hunt or exactsearch
    Supports a single request id at this time
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Identifier of a MalQuery request
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: download_a_file_indexed_by_malquery
  label: Malquery - Download a file indexed by MalQuery Specify the file using its SHA256 Only
    one file is supported at this time
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The file SHA256.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: revoke_oauth2_access_token
  label: OAuth2 - Revoke a previously issued OAuth2 access token before the end of its standard
    30minute lifespan
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Accept-Encoding: application/json
      Content-Type: application/x-www-form-urlencoded
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_device_control_policy_ids
  label: Device Control Policies - Search for Device Control Policies in your environment by providing an FQL
    filter and paging details Returns a set of Device Control Policy IDs which match
    the filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_device_control_policy_members
  label: Device Control Policies - Search for members of a Device Control Policy in your environment by providing
    an FQL filter and paging details Returns a set of host details which match the
    filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Device Control Policy to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_device_control_policies
  label: Device Control Policies - Search for Device Control Policies in your environment by providing an FQL
    filter and paging details Returns a set of Device Control Policies which match
    the filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_device_control_policy_member_ids
  label: Device Control Policies - Search for members of a Device Control Policy in your environment by providing
    an FQL filter and paging details Returns a set of Agent IDs which match the filter
    criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Device Control Policy to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: set_precedence_of_device_control_policies
  label: Device Control Policies - Sets the precedence of Device Control Policies based on the order of IDs
    specified in the request The first ID specified will have the highest precedence
    and the last ID specified will have the lowest You must specify all nonDefault
    Policies for a platform when updating precedence
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: perform_action_on_the_device_control_policies
  label: Device Control Policies - Perform the specified action on the Device Control Policies specified in
    the request
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The action to perform
    name: action_name
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_a_set_of_device_control_policies
  label: Device Control Policies - Retrieve a set of Device Control Policies by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Device Control Policies to return
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_a_set_of_device_control_policies
  label: Device Control Policies - Delete a set of Device Control Policies by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Device Control Policies to delete
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_device_control_policies
  label: Device Control Policies - Create Device Control Policies by specifying details about the policy to
    create
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_device_control_policies
  label: Device Control Policies - Update Device Control Policies by specifying the ID of the policy and details
    to update
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_firewall_policies
  label: Firewall Policies - Search for Firewall Policies in your environment by providing an FQL filter
    and paging details Returns a set of Firewall Policy IDs which match the filter
    criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: set_precedence_of_firewall_policies
  label: Firewall Policies - Sets the precedence of Firewall Policies based on the order of IDs specified
    in the request The first ID specified will have the highest precedence and the
    last ID specified will have the lowest You must specify all nonDefault Policies
    for a platform when updating precedence
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: perform_action_on_the_firewall_policies
  label: Firewall Policies - Perform the specified action on the Firewall Policies specified in the request
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The action to perform
    name: action_name
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_firewall_policy_member_ids
  label: Firewall Policies - Search for members of a Firewall Policy in your environment by providing
    an FQL filter and paging details Returns a set of Agent IDs which match the filter
    criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Firewall Policy to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_firewall_policies
  label: Firewall Policies - Search for Firewall Policies in your environment by providing an FQL filter
    and paging details Returns a set of Firewall Policies which match the filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_a_set_of_firewall_policies
  label: Firewall Policies - Retrieve a set of Firewall Policies by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Firewall Policies to return
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_a_set_of_firewall_policies
  label: Firewall Policies - Delete a set of Firewall Policies by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Firewall Policies to delete
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_firewall_policies
  label: Firewall Policies - Create Firewall Policies by specifying details about the policy to create
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The policy ID to be cloned from
    name: clone_id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_firewall_policies
  label: Firewall Policies - Update Firewall Policies by specifying the ID of the policy and details to
    update
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_firewall_policy_members
  label: Firewall Policies - Search for members of a Firewall Policy in your environment by providing
    an FQL filter and paging details Returns a set of host details which match the
    filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Firewall Policy to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_prevention_policy_members
  label: Prevention Policies - Search for members of a Prevention Policy
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Prevention Policy to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_prevention_policy_ids
  label: Prevention Policies - Search for Prevention Policies in your environment by providing an FQL filter
    and paging details Returns a set of Prevention Policy IDs which match the filter
    criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_prevention_policies
  label: Prevention Policies - Search for Prevention Policies in your environment by providing an FQL filter
    and paging details Returns a set of Prevention Policies which match the filter
    criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: set_precedence_of_prevention_policies
  label: Prevention Policies - Sets the precedence of Prevention Policies based on the order of IDs specified
    in the request The first ID specified will have the highest precedence and the
    last ID specified will have the lowest You must specify all nonDefault Policies
    for a platform when updating precedence
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_a_set_of_prevention_policies
  label: Prevention Policies - Retrieve a set of Prevention Policies by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Prevention Policies to return
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_a_set_of_prevention_policies
  label: Prevention Policies - Delete a set of Prevention Policies by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Prevention Policies to delete
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_prevention_policies
  label: Prevention Policies - Create Prevention Policies by specifying details about the policy to create
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_prevention_policies
  label: Prevention Policies - Update Prevention Policies by specifying the ID of the policy and details
    to update
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_prevention_policy_member_ids
  label: Prevention Policies - Search for members of a Prevention Policy in your environment by providing
    an FQL filter and paging details Returns a set of Agent IDs which match the filter
    criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Prevention Policy to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: perform_action_on_the_prevention_policies
  label: Prevention Policies - Perform the specified action on the Prevention Policies specified in the
    request
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The action to perform
    name: action_name
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: set_precedence_of_response_policies
  label: Response Policies - Sets the precedence of Response Policies based on the order of IDs specified
    in the request The first ID specified will have the highest precedence and the
    last ID specified will have the lowest You must specify all nonDefault Policies
    for a platform when updating precedence
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_response_policy_members
  label: Response Policies - Search for members of a Response policy in your environment by providing
    an FQL filter and paging details Returns a set of host details which match the
    filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Response policy to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_response_policy_member_ids
  label: Response Policies - Search for members of a Response policy in your environment by providing
    an FQL filter and paging details Returns a set of Agent IDs which match the filter
    criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Response policy to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: perform_action_on_the_response_policies
  label: Response Policies - Perform the specified action on the Response Policies specified in the request
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The action to perform
    name: action_name
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_a_set_of_response_policies
  label: Response Policies - Retrieve a set of Response Policies by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the RTR Policies to return
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_a_set_of_response_policies
  label: Response Policies - Delete a set of Response Policies by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Response Policies to delete
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_response_policies
  label: Response Policies - Create Response Policies by specifying details about the policy to create
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_response_policies
  label: Response Policies - Update Response Policies by specifying the ID of the policy and details to
    update
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_response_policy_ids
  label: Response Policies - Search for Response Policies in your environment by providing an FQL filter
    with sort andor paging details This returns a set of Response Policy IDs that
    match the given criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to determine the results.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset of the first record to retrieve from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum number of records to return [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort results by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_response_policies
  label: Response Policies - Search for Response Policies in your environment by providing an FQL filter
    and paging details Returns a set of Response Policies which match the filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_sensor_update_policies
  label: Sensor Update Policies - Search for Sensor Update Policies in your environment by providing an FQL
    filter and paging details Returns a set of Sensor Update Policies which match
    the filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_sensor_update_policy_member_ids
  label: Sensor Update Policies - Search for members of a Sensor Update Policy in your environment by providing
    an FQL filter and paging details Returns a set of Agent IDs which match the filter
    criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Sensor Update Policy to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: perform_action_on_the_sensor_update_policies
  label: Sensor Update Policies - Perform the specified action on the Sensor Update Policies specified in the
    request
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The action to perform
    name: action_name
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_sensor_update_policy_members
  label: Sensor Update Policies - Search for members of a Sensor Update Policy in your environment by providing
    an FQL filter and paging details Returns a set of host details which match the
    filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The ID of the Sensor Update Policy to search for members of
    name: id
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_available_builds_for_use_with_sensor_update_policies
  label: Sensor Update Policies - Retrieve available builds for use with Sensor Update Policies
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The platform to return builds for
    name: platform
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_sensor_update_policy_ids
  label: Sensor Update Policies - Search for Sensor Update Policies in your environment by providing an FQL
    filter and paging details Returns a set of Sensor Update Policy IDs which match
    the filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_sensor_update_policies_with_additional_support_for_uninstall_protection
  label: Sensor Update Policies - Search for Sensor Update Policies with additional support for uninstall protection
    in your environment by providing an FQL filter and paging details Returns a set
    of Sensor Update Policies which match the filter criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-5000]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The property to sort by
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_a_set_of_sensor_update_policies_with_additional_support_for_uninstall_protection
  label: Sensor Update Policies - Retrieve a set of Sensor Update Policies with additional support for uninstall
    protection by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Sensor Update Policies to return
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_sensor_update_policies
  label: Sensor Update Policies - Create Sensor Update Policies by specifying details about the policy to create
    with additional support for uninstall protection
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_sensor_update_policies
  label: Sensor Update Policies - Update Sensor Update Policies by specifying the ID of the policy and details
    to update with additional support for uninstall protection
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_an_uninstall_token_for_a_specific_device
  label: Sensor Update Policies - Reveals an uninstall token for a specific device To retrieve the bulk maintenance
    token pass the value MAINTENANCE as the value for device_id
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: set_precedence_of_sensor_update_policies
  label: Sensor Update Policies - Sets the precedence of Sensor Update Policies based on the order of IDs specified
    in the request The first ID specified will have the highest precedence and the
    last ID specified will have the lowest You must specify all nonDefault Policies
    for a platform when updating precedence
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_a_set_of_sensor_update_policies
  label: Sensor Update Policies - Retrieve a set of Sensor Update Policies by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Sensor Update Policies to return
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_a_set_of_sensor_update_policies
  label: Sensor Update Policies - Delete a set of Sensor Update Policies by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the Sensor Update Policies to delete
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_sensor_update_policies
  label: Sensor Update Policies - Create Sensor Update Policies by specifying details about the policy to create
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_sensor_update_policies
  label: Sensor Update Policies - Update Sensor Update Policies by specifying the ID of the policy and details
    to update
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_set_of_ioa_exclusions
  label: IOA Exclusions - Get a set of IOA Exclusions by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The ids of the exclusions to retrieve
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_the_ioa_exclusions_by_id
  label: IOA Exclusions - Delete the IOA exclusions by id
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The ids of the exclusions to delete
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Explains why this exclusions was deleted
    name: comment
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_the_ioa_exclusions
  label: IOA Exclusions - Create the IOA exclusions
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_the_ioa_exclusions
  label: IOA Exclusions - Update the IOA exclusions
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_ioa_exclusions
  label: IOA Exclusions - Search for IOA exclusions
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-500]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The sort expression that should be used to sort the results.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_ml_exclusions
  label: ML Exclusions - Search for ML exclusions
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-500]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The sort expression that should be used to sort the results.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_set_of_ml_exclusions
  label: ML Exclusions - Get a set of ML Exclusions by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The ids of the exclusions to retrieve
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_the_ml_exclusions_by_id
  label: ML Exclusions - Delete the ML exclusions by id
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The ids of the exclusions to delete
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Explains why this exclusions was deleted
    name: comment
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_the_ml_exclusions
  label: ML Exclusions - Create the ML exclusions
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_the_ml_exclusions
  label: ML Exclusions - Update the ML exclusions
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_set_of_sensor_visibility_exclusions
  label: Sensor Visibility Exclusions - Get a set of Sensor Visibility Exclusions by specifying their IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The ids of the exclusions to retrieve
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_the_sensor_visibility_exclusions_by_id
  label: Sensor Visibility Exclusions - Delete the sensor visibility exclusions by id
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The ids of the exclusions to delete
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Explains why this exclusions was deleted
    name: comment
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_the_sensor_visibility_exclusions
  label: Sensor Visibility Exclusions - Create the sensor visibility exclusions
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_the_sensor_visibility_exclusions
  label: Sensor Visibility Exclusions - Update the sensor visibility exclusions
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: search_for_sensor_visibility_exclusions
  label: Sensor Visibility Exclusions - Search for sensor visibility exclusions
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The filter expression that should be used to limit the results.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving records from
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The maximum records to return. [1-500]
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The sort expression that should be used to sort the results.
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_status_of_an_executed_active_responder_command_on_a_single_host
  label: Real Time Response - Get status of an executed active_responder command on a single host
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Cloud Request ID of the executed command to query
    name: cloud_request_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Sequence ID that we want to retrieve. Command responses are chunked
      across sequences
    name: sequence_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: execute_an_active_responder_command_on_a_single_host
  label: Real Time Response - Execute an active responder command on a single host
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: batch_refresh_a_rtr_session_on_multiple_hosts_rtr_sessions_will_expire_after_10_minutes_unless_refreshed
  label: Real Time Response - Batch refresh a RTR session on multiple hosts RTR sessions will expire after
    10 minutes unless refreshed
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Timeout for how long to wait for the request in seconds, default
      timeout is 30 seconds. Maximum is 10 minutes.
    name: timeout
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Timeout duration for for how long to wait for the request in duration
      syntax. Example, `10s`. Valid units: `ns, us, ms, s, m, h`. Maximum is 10 minutes.'
    name: timeout_duration
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_queued_session_metadata_by_session_id
  label: Real Time Response - Get queued session metadata by session ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: refresh_a_session_timeout_on_a_single_host
  label: Real Time Response - Refresh a session timeout on a single host
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: batch_initialize_a_rtr_session_on_multiple_hosts__before_any_rtr_commands_can_be_used_an_active_session_is_needed_on_the_host
  label: Real Time Response - Batch initialize a RTR session on multiple hosts  Before any RTR commands
    can be used an active session is needed on the host
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Timeout for how long to wait for the request in seconds, default
      timeout is 30 seconds. Maximum is 10 minutes.
    name: timeout
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Timeout duration for for how long to wait for the request in duration
      syntax. Example, `10s`. Valid units: `ns, us, ms, s, m, h`. Maximum is 10 minutes.'
    name: timeout_duration
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_rtr_extracted_file_contents_for_specified_session_and_sha256
  label: Real Time Response - Get RTR extracted file contents for specified session and sha256
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: RTR Session id
    name: session_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Extracted SHA256 (e.g. 'efa256a96af3b556cd3fc9d8b1cf587d72807d7805ced441e8149fc279db422b')
    name: sha256
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Filename to use for the archive name and the file within the archive.
    name: filename
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_aggregates_on_session_data
  label: Real Time Response - Get aggregates on session data
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_a_session
  label: Real Time Response - Delete a session
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: RTR Session id
    name: session_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: initialize_a_new_session_with_the_rtr_cloud
  label: Real Time Response - Initialize a new session with the RTR cloud
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_a_queued_session_command
  label: Real Time Response - Delete a queued session command
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: RTR Session id
    name: session_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Cloud Request ID of the executed command to query
    name: cloud_request_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_list_of_session_ids
  label: Real Time Response - Get a list of session_ids
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return ids.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of ids to return.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Sort by spec. Ex: ''date_created|asc''.'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Optional filter criteria in the form of an FQL query. For more information
      about FQL queries, see our [FQL documentation in Falcon](https://falcon.crowdstrike.com/support/documentation/45/falcon-query-language-feature-guide).
      "user_id" can accept a special value '@me' which will restrict results to records
      with current user's ID.
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_the_status_of_batch_get_command__will_return_successful_files_when_they_are_finished_processing
  label: Real Time Response - retrieve the status of the specified batch get command  Will return successful
    files when they are finished processing
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Batch Get Command Request ID received from `/real-time-response/combined/get-command/v1`
    name: batch_get_cmd_req_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Timeout for how long to wait for the request in seconds, default
      timeout is 30 seconds. Maximum is 10 minutes.
    name: timeout
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Timeout duration for for how long to wait for the request in duration
      syntax. Example, `10s`. Valid units: `ns, us, ms, s, m, h`. Maximum is 10 minutes.'
    name: timeout_duration
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: batch_executes_get_command_across_hosts_to_retrieve_files_after_this_call_is_made_get_realtimeresponsecombinedbatchgetcommandv1_is_used_to_query_for_the_results
  label: Real Time Response - Batch executes get command across hosts to retrieve files After this call
    is made GET realtimeresponsecombinedbatchgetcommandv1 is used to query for the
    results
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Timeout for how long to wait for the request in seconds, default
      timeout is 30 seconds. Maximum is 10 minutes.
    name: timeout
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Timeout duration for for how long to wait for the request in duration
      syntax. Example, `10s`. Valid units: `ns, us, ms, s, m, h`. Maximum is 10 minutes.'
    name: timeout_duration
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: batch_executes_a_rtr_readonly_command
  label: Real Time Response - Batch executes a RTR readonly command across the hosts mapped to the given
    batch ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Timeout for how long to wait for the request in seconds, default
      timeout is 30 seconds. Maximum is 10 minutes.
    name: timeout
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Timeout duration for for how long to wait for the request in duration
      syntax. Example, `10s`. Valid units: `ns, us, ms, s, m, h`. Maximum is 10 minutes.'
    name: timeout_duration
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_session_metadata_by_session_id
  label: Real Time Response - Get session metadata by session id
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_list_of_files_for_rtr_session
  label: Real Time Response - Get a list of files for the specified RTR session
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: RTR Session id
    name: session_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_a_rtr_session_file
  label: Real Time Response - Delete a RTR session file
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: RTR Session file id
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: RTR Session id
    name: session_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_status_of_an_executed_command_on_a_single_host
  label: Real Time Response - Get status of an executed command on a single host
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Cloud Request ID of the executed command to query
    name: cloud_request_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Sequence ID that we want to retrieve. Command responses are chunked
      across sequences
    name: sequence_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: execute_a_command_on_a_single_host
  label: Real Time Response - Execute a command on a single host
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: batch_executes_a_rtr_active_responder_command
  label: Real Time Response - Batch executes a RTR active_responder command across the hosts mapped to the
    given batch ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Timeout for how long to wait for the request in seconds, default
      timeout is 30 seconds. Maximum is 10 minutes.
    name: timeout
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Timeout duration for for how long to wait for the request in duration
      syntax. Example, `10s`. Valid units: `ns, us, ms, s, m, h`. Maximum is 10 minutes.'
    name: timeout_duration
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_putfiles_based_on_the_ids_given
  label: Real Time Response Admin - Get putfiles based on the IDs given These are used for the RTR put command
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: File IDs
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_a_putfile_based_on_the_ids_given
  label: Real Time Response Admin - Delete a putfile based on the ID given  Can only delete one file at a time
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: File id
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: upload_a_new_putfile_to_use_for_the_rtr_put_command
  label: Real Time Response Admin - Upload a new putfile to use for the RTR put command
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_status_of_an_executed_rtr_administrator_command_on_a_single_host
  label: Real Time Response Admin - Get status of an executed RTR administrator command on a single host
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Cloud Request ID of the executed command to query
    name: cloud_request_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Sequence ID that we want to retrieve. Command responses are chunked
      across sequences
    name: sequence_id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: execute_a_rtr_administrator_command_on_a_single_host
  label: Real Time Response Admin - Execute a RTR administrator command on a single host
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_list_of_putfile_ids
  label: Real Time Response Admin - Get a list of putfile IDs that are available to the user for the put command
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Optional filter criteria in the form of an FQL query. For more information
      about FQL queries, see our [FQL documentation in Falcon](https://falcon.crowdstrike.com/support/documentation/45/falcon-query-language-feature-guide).
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return ids.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of ids to return.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Sort by spec. Ex: ''created_at|asc''.'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_a_list_of_custom_script_ids
  label: Real Time Response Admin - Get a list of custom_script IDs that are available to the user for the runscript
    command
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Optional filter criteria in the form of an FQL query. For more information
      about FQL queries, see our [FQL documentation in Falcon](https://falcon.crowdstrike.com/support/documentation/45/falcon-query-language-feature-guide).
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return ids.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of ids to return.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Sort by spec. Ex: ''created_at|asc''.'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_custom_scripts_based_on_the_ids_given
  label: Real Time Response Admin - Get custom_scripts based on the IDs given These are used for the RTR runscript
    command
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: File IDs
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_a_custom_script_based_on_the_id_given
  label: Real Time Response Admin - Delete a custom_script based on the ID given  Can only delete one script at
    a time
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: File id
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: upload_a_new_custom_script_to_use
  label: Real Time Response Admin - Upload a new custom_script to use for the RTR runscript command
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: upload_a_new_scripts_to_replace_an_existing_one
  label: Real Time Response Admin - Upload a new scripts to replace an existing one
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: batch_executes_a_rtr_administrator_command
  label: Real Time Response Admin - Batch executes a RTR administrator command across the hosts mapped to the
    given batch ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Timeout for how long to wait for the request in seconds, default
      timeout is 30 seconds. Maximum is 10 minutes.
    name: timeout
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Timeout duration for for how long to wait for the request in duration
      syntax. Example, `10s`. Valid units: `ns, us, ms, s, m, h`. Maximum is 10 minutes.'
    name: timeout_duration
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_notifications_based_on_ids_notifications
  label: Recon - Delete notifications based on IDs Notifications cannot be recovered after
    they are deleted
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Notifications IDs.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_notification_status_or_assignee
  label: Recon - Update notification status or assignee Accepts bulk requests
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: query_notifications
  label: Recon - Query notifications based on provided criteria Use the IDs from this response
    to get the notification entities on GET entitiesnotificationsv1 or GET entitiesnotificationsdetailedv1
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return ids.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of ids to return.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Possible order by fields: created_date, updated_date. Ex: ''updated_date|desc''.'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'FQL query to filter notifications by. Possible filter properties
      are: [id cid user_uuid status rule_id rule_name rule_topic rule_priority item_type
      created_date updated_date]'
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Free text search across all indexed fields.
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_detailed_notifications_based_on_their_ids
  label: Recon - Get detailed notifications based on their IDs These include the raw intelligence
    content that generated the matchThis endpoint will return translated notification
    content The only target language available is English A single notification can
    be translated per request
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Notification IDs.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: preview_rules_notification_count_and_distribution
  label: Recon - Preview rules notification count and distribution This will return aggregations
    on channel count site
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_notification_aggregates
  label: Recon - Get notification aggregates
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_actions_based_on_their_ids
  label: Recon - Get actions based on their IDs IDs can be retrieved using the GET queriesactionsv1
    endpoint
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Action IDs.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_an_action_from_a_monitoring_rule_based_on_the_action_id
  label: Recon - Delete an action from a monitoring rule based on the action ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: ID of the action.
    name: id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_actions_for_a_monitoring_rule
  label: Recon - Create actions for a monitoring rule Accepts a list of actions that will
    be attached to the monitoring rule
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "actions": "${actions}",
        "rule_id": "${rule_id}"
      }
    value: |-
      {
        "actions": "${actions}",
        "rule_id": "${rule_id}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_an_action_for_a_monitoring_rule
  label: Recon - Update an action for a monitoring rule
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "frequency": "${frequency}",
        "id": "${id}",
        "recipients": "${recipients}",
        "status": "${status}"
      }
    value: |-
      {
        "frequency": "${frequency}",
        "id": "${id}",
        "recipients": "${recipients}",
        "status": "${status}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: query_actions
  label: Recon - Query actions based on provided criteria Use the IDs from this response to
    get the action entities on GET entitiesactionsv1
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return IDs.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of IDs to return.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Possible order by fields: created_timestamp, updated_timestamp.
      Ex: ''updated_timestamp|desc''.'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'FQL query to filter actions by. Possible filter properties are:
      [id cid user_uuid rule_id type frequency recipients status created_timestamp
      updated_timestamp]'
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Free text search across all indexed fields
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: query_monitoring_rules
  label: Recon - Query monitoring rules based on provided criteria Use the IDs from this response
    to fetch the rules on entitiesrulesv1
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Starting index of overall result set from which to return ids.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Number of ids to return.
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Possible order by fields: created_timestamp, last_updated_timestamp.
      Ex: ''last_updated_timestamp|desc''.'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'FQL query to filter rules by. Possible filter properties are: [id
      cid user_uuid topic priority permissions filter status created_timestamp last_updated_timestamp]'
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Free text search across all indexed fields.
    name: q
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_notifications_based_on_their_ids
  label: Recon - Get notifications based on their IDs IDs can be retrieved using the GET queriesnotificationsv1
    endpoint This endpoint will return translated notification content The only target
    language available is English
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Notification IDs.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_detailed_notifications_based_on_their_ids_with_raw_intelligence_content_that_generated_the_match
  label: Recon - Get detailed notifications based on their IDs These include the raw intelligence
    content that generated the match
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Notification IDs.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_monitoring_rules_rules_by_provided_ids
  label: Recon - Get monitoring rules rules by provided IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: IDs of rules.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: delete_monitoring_rules
  label: Recon - Delete monitoring rules
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: IDs of rules.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: create_monitoring_rules
  label: Recon - Create monitoring rules
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: update_monitoring_rules
  label: Recon - Update monitoring rules
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_notifications_based_on_their_ids
  label: Recon - Get notifications based on their IDs IDs can be retrieved using the GET queriesnotificationsv1
    endpoint
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Notification IDs.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: upload_a_file_for_further_cloud_analysis
  label: Sample Uploads - Upload a file for further cloud analysis After uploading call the specific
    analysis API endpoint
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Name of the file.
    name: file_name
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: A descriptive comment to identify the file for other users.
    name: comment
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: "Defines visibility of this file in Falcon MalQuery, either via the
      API or the Falcon console.\n\n- `true`: File is only shown to users within your
      customer account\n- `false`: File can be seen by other CrowdStrike customers
      \n\nDefault: `true`."
    name: is_confidential
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: retrieve_the_file_associated_with_the_given_id_sha256
  label: Sample Uploads - retrieve the file associated with the given ID SHA256
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The file SHA256.
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Flag whether the sample should be zipped and password protected with
      pass='infected'
    name: password_protected
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: find_ids_for_submitted_scans
  label: Quick Scan - Find IDs for submitted scans by providing an FQL filter and paging details
    Returns a set of volume IDs that match your criteria
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Optional filter and sort criteria in the form of an FQL query. For
      more information about FQL queries, see [our FQL documentation in Falcon](https://falcon.crowdstrike.com/support/documentation/45/falcon-query-language-feature-guide).
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The offset to start retrieving submissions from.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Maximum number of volume IDs to return. Max: 5000.'
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Sort order: `asc` or `desc`.'
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_scans_aggregations
  label: Quick Scan - Get scans aggregations
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: body
    example: |-
      {
        "date_ranges": "${date_ranges}",
        "field": "${field}",
        "filter": "${filter}",
        "interval": "${interval}",
        "min_doc_count": "${min_doc_count}",
        "missing": "${missing}",
        "name": "${name}",
        "q": "${q}",
        "ranges": "${ranges}",
        "size": "${size}",
        "sort": "${sort}",
        "sub_aggregates": "${sub_aggregates}",
        "time_zone": "${time_zone}",
        "type": "${type}"
      }
    value: |-
      {
        "date_ranges": "${date_ranges}",
        "field": "${field}",
        "filter": "${filter}",
        "interval": "${interval}",
        "min_doc_count": "${min_doc_count}",
        "missing": "${missing}",
        "name": "${name}",
        "q": "${q}",
        "ranges": "${ranges}",
        "size": "${size}",
        "sort": "${sort}",
        "sub_aggregates": "${sub_aggregates}",
        "time_zone": "${time_zone}",
        "type": "${type}"
      }
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: check_the_status_of_a_volume_scan
  label: Quick Scan - Check the status of a volume scan Time required for analysis increases with
    the number of samples in a volume but usually it should take less than 1 minute
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: ID of a submitted scan
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: submit_a_volume_of_files_for_ml_scanning
  label: Quick Scan - Submit a volume of files for ml scanning Time required for analysis increases
    with the number of samples in a volume but usually it should take less than 1
    minute
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_sensor_installer_ids_by_provided_query
  label: Sensor Download - Get sensor installer IDs by provided query
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The first item to return, where 0 is the latest item. Use with the
      limit parameter to manage pagination of results.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'The number of items to return in this response (default: 100, max:
      500). Use with the offset parameter to manage pagination of results.'
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Sort items using their properties. Common sort options include:

      <ul><li>version|asc</li><li>release_date|desc</li></ul>
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Filter items using a query in Falcon Query Language (FQL). An asterisk wildcard * includes all results.

      Common filter options include:
      <ul><li>platform:"windows"</li><li>version:>"5.2"</li></ul>
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_sensor_installer_details_by_provided_query
  label: Sensor Download - Get sensor installer details by provided query
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: The first item to return, where 0 is the latest item. Use with the
      limit parameter to manage pagination of results.
    name: offset
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'The number of items to return in this response (default: 100, max:
      500). Use with the offset parameter to manage pagination of results.'
    name: limit
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Sort items using their properties. Common sort options include:

      <ul><li>version|asc</li><li>release_date|desc</li></ul>
    name: sort
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: |-
      Filter items using a query in Falcon Query Language (FQL). An asterisk wildcard * includes all results.

      Common filter options include:
      <ul><li>platform:"windows"</li><li>version:>"5.2"</li></ul>
    name: filter
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_sensor_installer_details_by_provided_sha256_ids
  label: Sensor Download - Get sensor installer details by provided SHA256 IDs
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: The IDs of the installers
    name: ids
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: download_sensor_installer_by_sha256_id
  label: Sensor Download - Download sensor installer by SHA256 ID
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: SHA256 of the installer to download
    name: id
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_ccid_to_use_with_sensor_installers
  label: Sensor Download - Get CCID to use with sensor installers
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: refresh_an_active_event_stream
  label: Event Streams - Refresh an active event stream Use the URL shown in a GET sensorsentitiesdatafeedv2
    response
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: Action name. Allowed value is refresh_active_stream_session.
    name: action_name
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Label that identifies your connection. Max: 32 alphanumeric characters
      (a-z, A-Z, 0-9).'
    name: appId
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Generated by shuffler.io OpenAPI
    name: partition
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
- description: ""
  name: get_all_event_streams
  label: Event Streams - Discover all event streams in your environment
  nodetype: action
  environment: Shuffle
  sharing: false
  privateid: ""
  publicid: ""
  appid: ""
  tags: []
  tested: false
  parameters:
  - description: 'Label that identifies your connection. Max: 32 alphanumeric characters
      (a-z, A-Z, 0-9).'
    name: appId
    example: ""
    multiline: false
    options: []
    required: true
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit headers
    name: headers
    example: ""
    value: |-
      Authorization: Bearer $auth.access_token
      Accept-Encoding: application/json
      Content-Type: application/json
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: Add or edit queries
    name: queries
    example: view=basic&redirect=test
    multiline: true
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  - description: 'Format for streaming events. Valid values: json, flatjson'
    name: format
    example: ""
    multiline: false
    options: []
    required: false
    configuration: false
    tags: []
    schema:
      type: string
    skip_multicheck: false
    unique_toggled: false
  executionvariable:
    description: ""
    id: ""
    name: ""
    value: ""
  returns:
    example: ""
    schema:
      type: string
  authenticationid: ""
  example: ""
  auth_not_required: false
  source_workflow: ""
authentication:
  required: true
  parameters:
  - description: ""
    id: ""
    name: client_id
    example: '******'
    value: ""
    multiline: false
    required: true
    in: ""
    schema:
      type: string
    scheme: ""
  - description: ""
    id: ""
    name: client_secret
    example: '******'
    value: ""
    multiline: false
    required: true
    in: ""
    schema:
      type: string
    scheme: ""
  - description: The URL of the app
    id: ""
    name: url
    example: https://api.crowdstrike.com
    value: https://api.crowdstrike.com
    multiline: false
    required: true
    in: ""
    schema:
      type: string
    scheme: ""
tags: []
categories: []
created: 0
edited: 0
lastruntime: 0
versions: []
loopversions: []
owner: b5ee0878-2de4-4182-92af-bf67ec6526f5
public: false
referenceorg: ""
referenceurl: ""
large_image: data:image/jpeg;base64,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
