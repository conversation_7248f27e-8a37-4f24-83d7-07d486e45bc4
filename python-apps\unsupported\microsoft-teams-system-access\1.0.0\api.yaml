app_version: 1.0.0
name: Microsoft Teams System Access
description: An app for the Microsoft teams WITHOUT delegated access
contact_info:
  name: "@frikkylikeme"
  url: https://frikky.com
  email: <EMAIL>
tags:
  - Communication 
  - Comms
  - Chat 
categories:
  - Comms 
authentication:
  required: true
  parameters:
    - name: tenant_id 
      description: The tenant of the OAuth client
      example: "*****"
      required: true
      schema:
        type: string  
    - name: client_id
      description: The client id to use
      example: "*****"
      multiline: false
      required: true
      schema:
          type: string 
    - name: client_secret
      description: The secret key to use
      multiline: false
      example: "*****"
      required: true
      schema:
        type: string         
actions:
  - name: list_teams 
    description: Returns all teams for a user 
    parameters:
    - name: user_id 
      description: The user to check for
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
  - name: list_members_in_team 
    description: Returns all members in a team
    parameters:
    - name: team_id 
      description: The team to check
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
  - name: list_channels_in_team
    description: Returns all channels for a team
    parameters:
    - name: team_id 
      description: The user to check for
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
  - name: create_channel_in_team 
    description: Creates a channel in a team 
    parameters:
    - name: team_id 
      description: The user to check for
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
    - name: name 
      description: Add person to channel
      example: "The coolest channel" 
      required: true
      schema:
        type: string  
    - name: description 
      description: The description to use for the channel
      example: "And it really is only for cool people" 
      required: true
      schema:
        type: string  
  - name: add_user_to_channel 
    description: Adds a user to a channel
    parameters:
    - name: team_id 
      description: The user to check for
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
    - name: channel_id 
      description: The channel ID to use
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
    - name: user_id 
      description: The user to add
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
    - name: role 
      description: The role to give them
      required: true
      options:
        - member
        - owner
      schema:
        type: string  
  #- name: send_message_to_channel 
  #  description: Sends a message to a channel
  #  parameters:
  #  - name: team_id 
  #    description: The user to check for
  #    example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
  #    required: true
  #    schema:
  #      type: string  
  #  - name: channel_id 
  #    description: The channel ID to use
  #    example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
  #    required: true
  #    schema:
  #      type: string  
  #  - name: user_id 
  #    description: The user ID to use
  #    example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
  #    required: true
  #    schema:
  #      type: string  
  #  - name: message 
  #    description: The message to send
  #    example: "Have a nice weekend!!"
  #    required: true
  #    schema:
  #      type: string  
  - name: list_apps_in_team
    description: Deletes a channel from a team
    parameters:
    - name: team_id 
      description: The user to check for
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
  - name: get_app_in_team 
    description: Gets and app installed in a team
    parameters:
    - name: team_id 
      description: The user to check for
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
    - name: app_id 
      description: The app ID to use
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
  - name: add_webhook_to_team 
    description: Adds a webhook to a team
    parameters:
    - name: team_id 
      description: The user to check for
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
  - name: delete_channel 
    description: Deletes a channel from a team
    parameters:
    - name: team_id 
      description: The user to check for
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
    - name: channel_id 
      description: The channel ID to use
      example: "b6b6c99f-bf87-4815-9f62-82aef893c634" 
      required: true
      schema:
        type: string  
large_image: data:image/png;base64,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
