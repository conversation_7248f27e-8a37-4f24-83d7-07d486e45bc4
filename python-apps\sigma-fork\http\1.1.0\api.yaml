walkoff_version: 1.1.0
app_version: 1.1.0
name: http
description: HTTP app 
tags:
  - Testing 
  - HTTP
categories:
  - Testing 
  - HTTP
contact_info:
  name: "@frikkylikeme"
  url: https://github.com/frikky
  email: "<EMAIL>"
actions:
  - name: GET
    description: Runs a GET request towards the specified endpoint
    parameters:
      - name: url 
        description: The URL to get
        multiline: false
        example: "https://example.com"
        required: true
        schema:
          type: string
      - name: headers 
        description: Headers to use 
        multiline: true 
        required: false 
        example: "Authorization: Bearer asd\nContent-Type: application/json"
        schema:
          type: string
      - name: username 
        description: The username to use
        multiline: false 
        required: false 
        example: "Username"
        schema:
          type: string
      - name: password 
        description: The password to use
        multiline: false 
        required: false 
        example: "*****"
        schema:
          type: string
      - name: verify 
        description: Check certificate
        multiline: false 
        options:
          - false 
          - true
        required: false 
        example: "false"
        schema:
          type: bool 
    returns:
      schema:
        type: string
      example: "404 NOT FOUND"
  - name: POST 
    description: Runs a POST request towards the specified endpoint
    parameters:
      - name: url 
        description: The URL to post to
        multiline: false
        example: "https://example.com"
        required: true
        schema:
          type: string
      - name: body 
        description: The body to use 
        multiline: true 
        example: "{\n\t'json': 'blob'\n}"
        required: false 
        schema:
          type: string
      - name: headers 
        description: Headers to use 
        multiline: true 
        required: false 
        example: "Authorization: Bearer asd\nContent-Type: application/json"
        schema:
          type: string
      - name: username 
        description: The username to use
        multiline: false 
        required: false 
        example: "Username"
        schema:
          type: string
      - name: password 
        description: The password to use
        multiline: false 
        required: false 
        example: "*****"
        schema:
          type: string
      - name: verify 
        description: Whether to check the certificate or not
        multiline: false 
        required: false 
        options:
          - false 
          - true
        example: "false"
        schema:
          type: bool 
    returns:
      schema:
        type: string
      example: "404 NOT FOUND"
  - name: PATCH
    description: Runs a PATCHrequest towards the specified endpoint
    parameters:
      - name: url 
        description: The URL to post to
        multiline: false
        example: "https://example.com"
        required: true
        schema:
          type: string
      - name: body 
        description: The body to use 
        multiline: true 
        example: "{\n\t'json': 'blob'\n}"
        required: false 
        schema:
          type: string
      - name: headers 
        description: Headers to use 
        multiline: true 
        required: false 
        example: "Authorization: Bearer asd\nContent-Type: application/json"
        schema:
          type: string
      - name: username 
        description: The username to use
        multiline: false 
        required: false 
        example: "Username"
        schema:
          type: string
      - name: password 
        description: The password to use
        multiline: false 
        required: false 
        example: "*****"
        schema:
          type: string
      - name: verify 
        description: Whether to check the certificate or not
        multiline: false 
        required: false 
        options:
          - false 
          - true
        example: "false"
        schema:
          type: bool 
    returns:
      schema:
        type: string
      example: "404 NOT FOUND"
  - name: PUT
    description: Runs a PUT request towards the specified endpoint
    parameters:
      - name: url 
        description: The URL to PUT to
        multiline: false
        example: "https://example.com"
        required: true
        schema:
          type: string
      - name: body 
        description: The body to use 
        multiline: true 
        example: "{\n\t'json': 'blob'\n}"
        required: false 
        schema:
          type: string
      - name: headers 
        description: Headers to use 
        multiline: true 
        required: false 
        example: "Authorization: Bearer asd\nContent-Type: application/json"
        schema:
          type: string
      - name: username 
        description: The username to use
        multiline: false 
        required: false 
        example: "Username"
        schema:
          type: string
      - name: password 
        description: The password to use
        multiline: false 
        required: false 
        example: "*****"
        schema:
          type: string
      - name: verify 
        description: Whether to check the certificate or not
        multiline: false 
        required: false 
        options:
          - false 
          - true
        example: "false"
        schema:
          type: bool 
    returns:
      schema:
        type: string
      example: "404 NOT FOUND"
  - name: DELETE
    description: Runs a DELETE request towards the specified endpoint
    parameters:
      - name: url 
        description: The URL to post to
        multiline: false
        example: "https://example.com"
        required: true
        schema:
          type: string
      - name: headers 
        description: Headers to use 
        multiline: true 
        required: false 
        example: "Authorization: Bearer asd\nContent-Type: application/json"
        schema:
          type: string
      - name: username 
        description: The username to use
        multiline: false 
        required: false 
        example: "Username"
        schema:
          type: string
      - name: password 
        description: The password to use
        multiline: false 
        required: false 
        example: "*****"
        schema:
          type: string
      - name: verify 
        description: Whether to check the certificate or not
        multiline: false 
        required: false 
        options:
          - true
          - false 
        example: "false"
        schema:
          type: bool 
    returns:
      schema:
        type: string
      example: "404 NOT FOUND"
  - name: HEAD
    description: Runs a HEAD request towards the specified endpoint
    parameters:
      - name: url 
        description: The URL to HEAD to
        multiline: false
        example: "https://example.com"
        required: true
        schema:
          type: string
      - name: headers 
        description: Headers to use 
        multiline: true 
        required: false 
        example: "Authorization: Bearer asd\nContent-Type: application/json"
        schema:
          type: string
      - name: username 
        description: The username to use
        multiline: false 
        required: false 
        example: "Username"
        schema:
          type: string
      - name: password 
        description: The password to use
        multiline: false 
        required: false 
        example: "*****"
        schema:
          type: string
      - name: verify 
        description: Whether to check the certificate or not
        multiline: false 
        required: false 
        options:
          - false 
          - true
        example: "false"
        schema:
          type: bool 
    returns:
      schema:
        type: string
      example: "404 NOT FOUND"
  - name: OPTIONS
    description: Runs a OPTIONS request towards the specified endpoint
    parameters:
      - name: url 
        description: The URL to HEAD to
        multiline: false
        example: "https://example.com"
        required: true
        schema:
          type: string
      - name: headers 
        description: Headers to use 
        multiline: true 
        required: false 
        example: "Authorization: Bearer asd\nContent-Type: application/json"
        schema:
          type: string
      - name: username 
        description: The username to use
        multiline: false 
        required: false 
        example: "Username"
        schema:
          type: string
      - name: password 
        description: The password to use
        multiline: false 
        required: false 
        example: "*****"
        schema:
          type: string
      - name: verify 
        description: Whether to check the certificate or not
        multiline: false 
        required: false 
        options:
          - false 
          - true
        example: "false"
        schema:
          type: bool 
    returns:
      schema:
        type: string
      example: "404 NOT FOUND"
  - name: curl 
    description: Run a curl command
    parameters:
      - name: statement 
        description: The curl command to run
        multiline: true
        example: "curl https://example.com"
        required: true
        schema:
          type: string
    returns:
      schema:
        type: string
large_image: data:image/png;base64,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
