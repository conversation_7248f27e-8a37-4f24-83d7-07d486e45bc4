app_version: 1.0.0
name: Active Directory
description: Active Directory and LDAP/LDAPS. For full usage of the action configure using LDAPS.
contact_info:
  name: "@d4rkw0lv3s"
  url: https://github.com/D4rkw0lv3s
  email: <EMAIL>
tags:
  - activedirectory
  - ldap 
  - ldaps
  - Azure AD
categories:
  - IAM
  - assets 
authentication:
  required: true
  parameters:
    - name: server
      description: "Server fqdn or ip address."
      example: "server-1.mycompany.com or 127.0.0.1"
      required: true
      schema:
        type: string
    - name: port
      description: "Server port."
      required: true
      example: "389"
      schema:
        type: string
    - name: domain
      description: "Domain to BIND to AD/LDAP with. Should JUST be the NetBIOSName from Get-Addomain"
      example: "ICPLAHD"
      required: true
      schema:
        type: string
    - name: login_user
      description: "Username to BIND to AD/LDAP with"
      example: "binduser"
      required: true
      schema:
        type: string
    - name: password
      description: "Password to BIND with."
      example: "Password1IsBad!"
      required: true
      schema:
        type: string
    - name: base_dn
      description: "Search Base DN"
      example: "OU=Users,DC=icplahd,DC=com"
      required: true
      schema:
        type: string
    - name: use_ssl
      description: "Use SSL Connection Security"
      required: true
      example: "True"
      options:
        - "true"
        - "false"
      schema:
        type: string
actions:
  - name: user_attributes
    description: Query AD for details about a specified user
    parameters:
      - name: samaccountname
        description: user to query
        required: true
        multiline: false
        example: 'user01'
        schema:
          type: string
      - name: search_base
        description: "If empty it will use the base_dn."
        required: false
        multiline: false
        example: "OU=Users,DC=mycompany,DC=com"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: set_password
    description: Set password for given user
    parameters:
      - name: samaccountname
        description: user to query
        required: true
        multiline: false
        example: 'user01'
        schema:
          type: string
      - name: new_password
        description: user new password
        required: true
        multiline: false
        example: 'Password1IsBad!'
        schema:
          type: string
      - name: repeat_password
        description: repeat the new password
        required: true
        multiline: false
        schema:
          type: string
      - name: search_base
        description: "If empty it will use the base_dn."
        required: false
        multiline: false
        example: "OU=Users,DC=mycompany,DC=com"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: change_password_at_next_logon
    description: Force user to change password at next logon
    parameters:
      - name: samaccountname
        description: user to query
        required: true
        multiline: false
        example: 'user01'
        schema:
          type: string
      - name: search_base
        description: "If empty it will use the base_dn."
        required: false
        multiline: false
        example: "OU=Users,DC=mycompany,DC=com"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: enable_user
    description: Enable User account
    parameters:
      - name: samaccountname
        description: user to query
        required: true
        multiline: false
        example: 'user01'
        schema:
          type: string
      - name: search_base
        description: "If empty it will use the base_dn."
        required: false
        multiline: false
        example: "OU=Users,DC=mycompany,DC=com"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: disable_user
    description: Disable User account
    parameters:
      - name: samaccountname
        description: user to query
        required: true
        multiline: false
        example: 'user01'
        schema:
          type: string
      - name: search_base
        description: "If empty it will use the base_dn."
        required: false
        multiline: false
        example: "OU=Users,DC=icplahd,DC=com"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: lock_user
    description: Lock User account
    parameters:
      - name: samaccountname
        description: user to lock
        required: true
        multiline: false
        example: 'user01'
        schema:
          type: string
      - name: search_base
        description: "If empty it will use the base_dn."
        required: false
        multiline: false
        example: "OU=Users,DC=icplahd,DC=com"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: unlock_user
    description: Unlock User account
    parameters:
      - name: samaccountname
        description: user to unlock
        required: true
        multiline: false
        example: 'user01'
        schema:
          type: string
      - name: search_base
        description: "If empty it will use the base_dn."
        required: false
        multiline: false
        example: "OU=Users,DC=icplahd,DC=com"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: change_user_password_at_next_login 
    description: Set given password for user at next login
    parameters:
      - name: samaccountname
        description: user to change password for
        required: true
        multiline: false
        example: 'user01'
        schema:
          type: string
      - name: search_base
        description: "If empty it will use the base_dn."
        required: false
        multiline: false
        example: "OU=Users,DC=icplahd,DC=com"
        schema:
          type: string
      - name: new_user_password
        description: "New password you want to set"
        required: true
        multiline: false
        example: "***"
        schema:
          type: string
      - name: repeat_new_user_password
        description: "Repeat new password you want to set"
        required: true
        multiline: false
        example: "***"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: add_user_to_group
    description: Add user to group
    parameters:
      - name: samaccountname
        description: user to change password for
        required: true
        multiline: false
        example: 'user01'
        schema:
          type: string
      - name: search_base
        description: "If empty it will use the base_dn."
        required: false
        multiline: false
        example: "OU=Users,DC=icplahd,DC=com"
        schema:
          type: string
      - name: group_name
        description: "Group you want to add user to"
        required: true
        multiline: false
        example: "Group name"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: remove_user_from_group
    description: Remove user from group
    parameters:
      - name: samaccountname
        description: user to change password for
        required: true
        multiline: false
        example: 'user01'
        schema:
          type: string
      - name: search_base
        description: "If empty it will use the base_dn."
        required: false
        multiline: false
        example: "OU=Users,DC=icplahd,DC=com"
        schema:
          type: string
      - name: group_name
        description: "Group you want to remove user from"
        required: true
        multiline: false
        example: "Group name"
        schema:
          type: string
    returns:
      schema:
        type: string
  
large_image: data:image/png;base64,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
