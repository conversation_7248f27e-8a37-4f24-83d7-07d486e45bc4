app_version: 1.0.0
name: Attack Predictor 
description: An app to predict Attack Techniques and Tactics from plain text. Based on rcATT
contact_info:
  name: "@frikkylikeme"
  url: https://github.com/vlegoy/rcATT
  email: <EMAIL>
tags:
  - Testing 
categories:
  - Testing 
actions:
  - name: predict 
    description: Predicts the 
    parameters:
      - name: data 
        description: The data to predict
        required: true
        multiline: true
        example: 'REDBALDKNIGHT, also known as BRONZE BUTLER and Tick, is a cyberespionage group known to target Japanese organizations such as government agencies (including defense) as well as those in biotechnology, electronics manufacturing, and industrial chemistry. Their campaigns employ the Daserf backdoor (detected by Trend Micro as BKDR_DASERF, otherwise known as Muirim and <PERSON>oupale) that has four main capabilities: execute shell commands, download and upload data, take screenshots, and log keystrokes.'
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: predict_file_content
    description: Predicts the 
    parameters:
      - name: file_id 
        description: The file to predict
        required: true
        multiline: false
        example: '24b5f1e8-3165-4af4-96ab-877d3581242e'
        schema:
          type: string
    returns:
      schema:
        type: string

large_image: data:image/jpg;base64,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****************************************************************************************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
