name: Automation - Add all new issues to roadmap project

on:
  issues:
    types:
      - opened

jobs:
  add-label:
    name: Add label to issue
    runs-on: ubuntu-latest
    steps:
    - uses: github/issue-labeler@v3.3 #May not be the latest version
      with:
        configuration-path: .github/labeler.yml
        repo-token: ${{ secrets.ADD_TO_PROJECT_PAT }}
        enable-versioned-regex: 0

  add-to-project:
    name: Add issue to project
    runs-on: ubuntu-latest
    steps:
      - uses: actions/add-to-project@v0.5.0
        with:
          project-url: https://github.com/orgs/Shuffle/projects/8
          github-token: ${{ secrets.ADD_TO_PROJECT_PAT }}
