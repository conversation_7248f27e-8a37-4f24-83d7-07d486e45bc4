app_version: 1.0.0
name: VulnDB
description: VulnDB vulnerability notifications (https://vulndb.cyberriskanalytics.com/)
contact_info:
  name: "@fritzbacke"
  url: https://github.com/fritzbacke
  email: <EMAIL>
tags:
  - Testing
  - Assets
  - Vulnerabilities
categories:
  - Assets
authentication:
  required: true
  parameters:
    - name: ClientID
      description: The client ID generated at VulnDB for this OAuth2 application
      example: ""
      required: true
      schema:
        type: string
    - name: ClientSecret
      description: The client secret generated at VulnDB for this OAuth2 application
      example: "*****"
      required: true
      schema:
        type: string
actions:
  - name: latest_20_vulns
    description: Return the 20 most recent vulnerabilities as JSON object
    returns:
      schema:
        type: string
large_image: data:image/jpg;base64,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
