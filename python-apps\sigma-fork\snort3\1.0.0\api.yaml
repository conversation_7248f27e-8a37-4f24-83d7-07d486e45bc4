---
app_version: 1.0.0
name: snort3
description: Analyzes pcap files based on set rules
tags:
  - Intel
  - Malware
  - File check
categories:
  - Intel
contact_info:
  name: "@synack3"
  url: https://gitlab.com/synack3
  email: <EMAIL>
actions:
  - name: simple_analyze_file
    description: Runs a custom snort3 config & rules file against a pcap file
    parameters:
      - name: config_file
        # yamllint disable-line rule:line-length
        description: File ID of a snort.lua file (Default use software-provided snort.lua)
        required: false
        multiline: false
        example: 2ff4c409-f66a-4bdc-bede-5dd5969a8c55
        schema:
          type: string
      - name: rules_file
        description: File ID of the .rules file.
        required: true
        multiline: false
        example: 2ff4c409-f66a-4bdc-bede-5dd5969a8c55
        schema:
          type: string
      - name: pcap_file
        description: File ID of the packet capture to analyze
        required: true
        multiline: false
        example: 2ff4c409-f66a-4bdc-bede-5dd5969a8c55
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: version_check
    description: Reports the version of snort installed
    returns:
      schema:
        type: string
  - name: custom_rule_scan
    # yamllint disable-line rule:line-length
    description: Use a snort3 rule provided as an execution argument to scan a pcap file.
    parameters:
      - name: config_file
        # yamllint disable-line rule:line-length
        description: File ID of a snort.lua file (Default use software-provided snort.lua)
        required: false
        multiline: false
        example: 2ff4c409-f66a-4bdc-bede-5dd5969a8c55
        schema:
          type: string
      - name: custom_rule
        description: One or more snort3 rules to leverage.
        required: true
        multiline: false
        # yamllint disable-line rule:line-length
        example: alert tcp any any -> any any (msg:"TCP Packet Found"; classtype:foo; sid:100000)
        schema:
          type: string
      - name: pcap_file
        description: File ID of the packet capture to analyze
        required: true
        multiline: false
        example: 2ff4c409-f66a-4bdc-bede-5dd5969a8c55
        schema:
          type: string
    returns:
      schema:
        type: string
# yamllint disable-line rule:line-length
large_image: data:image/png;base64,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
