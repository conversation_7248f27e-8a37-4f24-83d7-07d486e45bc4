---
app_version: 1.0.0
name: Shuffle Tools
description: A tool app for Shuffle
tags:
  - Testing
  - Shuffle
categories:
  - Testing
  - Shuffle
contact_info:
  name: "@frikkylikeme"
  url: https://shuffler.io
  email: <EMAIL>
actions:
  - name: repeat_back_to_me
    description: Repeats the call parameter
    parameters:
      - name: call
        description: The message to repeat
        required: true
        multiline: true
        example: "REPEATING: Hello world"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: router
    description: Reroutes information between different nodes
    returns:
      schema:
        type: string
  - name: get_cache_value
    description: Get a value saved to your organization in Shuffle
    parameters:
      - name: key
        description: The key to get
        required: true
        multiline: false
        example: "timestamp"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: set_cache_value
    description: Set a value to be saved to your organization in Shuffle.
    parameters:
      - name: key
        description: The key to set the value for
        required: true
        multiline: false
        example: "timestamp"
        schema:
          type: string
      - name: value
        description: The value to set
        required: true
        multiline: true
        example: "1621959545"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: send_sms_shuffle
    description: Send an SMS from Shuffle
    parameters:
      - name: apikey
        description: Your https://shuffler.io organization apikey
        multiline: false
        example: "https://shuffler.io apikey"
        required: true
        schema:
          type: string
      - name: phone_numbers
        description: The receivers of the SMS
        multiline: false
        example: "+4741323535,+8151023022"
        required: true
        schema:
          type: string
      - name: body
        description: The SMS to add to the numbers
        multiline: true
        example: "This is an alert from Shuffle :)"
        required: true
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: send_email_shuffle
    description: Send an email from Shuffle
    parameters:
      - name: apikey
        description: Your https://shuffler.io organization apikey
        multiline: false
        example: "https://shuffler.io apikey"
        required: true
        schema:
          type: string
      - name: recipients
        description: The recipients of the email
        multiline: false
        example: "<EMAIL>,<EMAIL>"
        required: true
        schema:
          type: string
      - name: subject
        description: The subject to use
        multiline: false
        example: "SOS this is an alert :o"
        required: true
        schema:
          type: string
      - name: body
        description: The body to add to the email
        multiline: true
        example: "This is an email alert from Shuffler.io :)"
        required: true
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: filter_list
    description: Takes a list and filters based on your data
    skip_multicheck: true
    parameters:
      - name: input_list
        description: The list to check
        required: true
        multiline: false
        example: '[{"data": "*******"}, {"data": "*******"}]'
        schema:
          type: string
      - name: field
        description: The field to check
        required: false
        multiline: false
        example: "data"
        schema:
          type: string
      - name: check
        description: Type of check
        required: true
        example: "equals"
        options:
          - equals
          - 'larger than'
          - 'less than'
          - is empty
          - contains
          - contains any of
          - starts with
          - ends with
          - field is unique
          - files by extension
        schema:
          type: string
      - name: value
        description: The value to check with
        required: false
        multiline: false
        example: "*******"
        schema:
          type: string
      - name: opposite
        description: Whether to add or to NOT add
        required: true
        options:
          - False
          - True
        multiline: false
        example: "false"
        schema:
          type: string
    returns:
      schema:
        type: string
  #- name: multi_list_filter
  #  description: Takes a list and filters based on your data
  #  skip_multicheck: true
  #  parameters:
  #    - name: input_list
  #      description: The list to check
  #      required: true
  #      multiline: false
  #      example: '[{"data": "*******"}, {"data": "*******"}]'
  #      schema:
  #        type: string
  #    - name: field
  #      description: The field to check
  #      required: true
  #      multiline: false
  #      example: "data"
  #      schema:
  #        type: string
  #    - name: check
  #      description: Type of check
  #      required: true
  #      example: "equals,equals"
  #      schema:
  #        type: string
  #    - name: value
  #      description: The value to check with
  #      required: true
  #      multiline: false
  #      example: "*******"
  #      schema:
  #        type: string
  #  returns:
  #    schema:
  #      type: string
  - name: parse_ioc
    description: Parse IOC's based on https://github.com/fhightower/ioc-finder
    parameters:
      - name: input_string
        description: The string to check
        required: true
        multiline: true
        example: "123ijq192.168.3.6kljqwiejs8 https://shuffler.io"
        schema:
          type: string
      - name: input_type
        description: The string to check
        required: false
        multiline: false
        example: "md5s"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: parse_file_ioc
    description: Parse IOC's based on https://github.com/fhightower/ioc-finder
    parameters:
      - name: file_ids
        description: The shuffle file to check
        required: true
        multiline: false
        schema:
          type: string
      - name: input_type
        description: The string to check
        required: false
        multiline: false
        example: "md5s"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: translate_value
    description: Takes a list of values and translates it in your input data
    parameters:
      - name: input_data
        description: The input data to use
        required: true
        multiline: true
        example: Hello this is an md5
        schema:
          type: string
      - name: translate_from
        description: The source items to look for
        required: true
        multiline: false
        example: sha256,md5,sha1
        schema:
          type: string
      - name: translate_to
        description: The destination data to change to
        required: true
        multiline: true
        example: hash
        schema:
          type: string
      - name: else_value
        description: The value to set if it DOESNT match. Default to nothing.
        required: false
        multiline: false
        example:
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: map_value
    description: Takes a mapping dictionary and translates the input data
    parameters:
      - name: input_data
        description: The input data to use
        required: true
        multiline: true
        example: $exec.field1
        schema:
          type: string
      - name: mapping
        description: The mapping dictionary
        required: true
        multiline: true
        example: |
          {
            "Low": 1,
            "Medium": 2,
            "High": 3,
          }
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: regex_replace
    description: Replace all instances matching a regular expression
    parameters:
      - name: input_data
        description: The input data to use
        required: true
        multiline: true
        example: $exec.http_headers
        schema:
          type: string
      - name: regex
        description: Your regular expression
        multiline: false
        example: "(Content-\\w+): (.*)"
        required: true
        schema:
          type: string
      - name: replace_string
        description: Replacement string (capture groups with \1 \2)
        multiline: true
        example: "Content header '\\1' = '\\2'"
        required: false
        schema:
          type: string
      - name: ignore_case
        description: "Make regex case insensitive (Default: False)"
        multiline: false
        example: "False"
        required: false
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: parse_list
    description: Parses a list and returns it as a json object
    parameters:
      - name: items
        description: List of items
        required: true
        multiline: true
        example: shuffler.io,test.com,test.no
        schema:
          type: string
      - name: splitter
        description: The splitter to use
        required: false
        multiline: false
        example: ","
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: execute_bash
    description: Runs bash with the data inputted available (TBD)
    parameters:
      - name: code
        description: The code to run
        required: true
        multiline: true
        example: echo "Hello"
        schema:
          type: string
      - name: shuffle_input
        description: Alternative data to add
        required: false
        multiline: true
        example: '{"data": "Hello world"}'
        schema:
          type: string
  - name: get_file_value
    description: This function is made for reading file(s), printing their data
    parameters:
      - name: filedata
        description: The files
        required: true
        multiline: true
        example: "REPEATING: Hello world"
        schema:
          type: file
    returns:
      schema:
        type: string
  - name: download_remote_file
    description: Downloads a file from a URL
    parameters:
      - name: url
        description:
        required: true
        multiline: false
        example: "https://secure.eicar.org/eicar.com.txt"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: get_file_meta
    description: Gets the file meta
    parameters:
      - name: file_id
        description:
        required: true
        multiline: false
        example: ""
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: delete_file
    description: Deletes a file based on ID
    parameters:
      - name: file_id
        description:
        required: true
        multiline: false
        example: "Some data to put in the file"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: extract_archive
    description: Extract compressed files, return file ids
    parameters:
      - name: file_ids
        description:
        required: true
        multiline: false
        schema:
          type: string
      - name: fileformat
        description:
        required: true
        multiline: false
        options:
          - zip
          - rar
          - 7zip
          - tar
          - tar.gz
        schema:
          type: string
      - name: password
        description:
        required: false
        multiline: false
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: inflate_archive
    description: Compress files in archive, return archive's file id
    parameters:
      - name: file_ids
        description:
        required: true
        multiline: true
        schema:
          type: string
      - name: fileformat
        description:
        required: true
        multiline: false
        options:
          - zip
          - 7zip
        schema:
          type: string
      - name: name
        description:
        required: false
        multiline: false
        schema:
          type: string
      - name: password
        description:
        required: false
        multiline: false
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: xml_json_convertor
    description: Converts xml to json and vice versa
    parameters:
      - name: convertto
        required: true
        multiline: false
        options:
          - json
          - xml
        schema:
          type: string
      - name: data
        description:
        required: true
        multiline: false
        example: 'xml data / json data'
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: date_to_epoch
    description: Converts a date field with a given format to an epoch time
    parameters:
      - name: input_data
        description: The input data to use
        required: true
        multiline: true
        example: 2010-11-04T04:15:22.123Z
        schema:
          type: dict
      - name: date_field
        description: The field containing the date to parse
        required: true
        multiline: false
        example: currentDateTime
        schema:
          type: string
      - name: date_format
        # yamllint disable-line rule:line-length
        description: The datetime format of the field to parse (strftime format).
        required: true
        multiline: false
        example: '%Y-%m-%dT%H:%M:%s.%f%Z'
        schema:
          type: string
    returns:
      schema:
        type: dict
  - name: compare_relative_date
    # yamllint disable-line rule:line-length
    description: Compares an input date to a relative date and returns a True/False result
    parameters:
      - name: input_data
        description: The input data to use
        required: true
        multiline: true
        example: 2010-11-04T04:15:22.123Z
        schema:
          type: string
      - name: date_format
        description: The format of the input date field  (strftime format)
        required: true
        multiline: false
        example: '%Y-%m-%dT%H:%M:%S.%f%Z'
        options:
          - '%Y-%m-%dT%H:%M%z'
          - '%Y-%m-%dT%H:%M:%SZ'
          - '%Y-%m-%dT%H:%M:%S%Z'
          - '%Y-%m-%dT%H:%M:%S%z'
          - '%Y-%m-%dT%H:%M:%S.%f%z'
          - '%Y-%m-%d'
          - '%H:%M:%S'
          - '%s'
        schema:
          type: string
      - name: equality_test
        description: How to compare the input date and offset date
        required: true
        multiline: false
        example: '>'
        options:
          - '>'
          - '<'
          - '='
          - '!='
          - '>='
          - '<='
        schema:
          type: string
      - name: offset
        description: Numeric offset from current time
        required: true
        multiline: false
        example: 60
        schema:
          type: string
      - name: units
        description: The units of the provided value
        required: true
        multiline: false
        example: 'seconds'
        options:
          - seconds
          - minutes
          - hours
          - days
        schema:
          type: string
      - name: direction
        description: Whether the comparison should be in the past or future
        required: true
        multiline: false
        example: 'ago'
        options:
          - ago
          - ahead
        schema:
          type: string
    returns:
      schema:
        type: strings
  - name: add_list_to_list
    description: Adds items of second list (list_two) to the first one (list_one)
    parameters:
      - name: list_one
        description: The first list
        multiline: true
        example: "{'key': 'value'}"
        required: true
        schema:
          type: string
      - name: list_two
        description: The second list to use
        multiline: true
        required: true
        example: "{'key2': 'value2'}"
        schema:
          type: string
  - name: merge_lists
    description: Merges two lists of same type AND length.
    parameters:
      - name: list_one
        description: The first list
        multiline: true
        example: "{'key': 'value'}"
        required: true
        schema:
          type: string
      - name: list_two
        description: The second list to use
        multiline: true
        required: true
        example: "{'key2': 'value2'}"
        schema:
          type: string
      - name: set_field
        description: If items in list 2 are strings, but first is JSON, sets the values to the specified key. Defaults to key "new_shuffle_key"
        required: false
        example: "json_key"
        schema:
          type: string
      - name: sort_key_list_one
        description: Sort by this key before using list one for merging
        required: false
        example: "json_key"
        schema:
          type: string
      - name: sort_key_list_two
        description: Sort by this key before using list two for merging
        required: false
        example: "json_key"
        schema:
          type: string
  - name: diff_lists
    description: Diffs two lists of strings or integers and finds what's missing
    parameters:
      - name: list_one
        description: The first list
        multiline: true
        example: "{'key': 'value'}"
        required: true
        schema:
          type: string
      - name: list_two
        description: The second list to use
        multiline: true
        required: true
        example: "{'key2': 'value2'}"
        schema:
          type: string
  - name: delete_json_keys
    description: Deletes keys in a json object
    parameters:
      - name: json_object
        description: The object to edit
        multiline: true
        example: "{'key': 'value', 'key2': 'value2', 'key3': 'value3'}"
        required: true
        schema:
          type: string
      - name: keys
        description: The key(s) to remove
        multiline: true
        required: true
        example: "key, key3"
        schema:
          type: string
  - name: convert_json_to_tags
    description: Creates key:value pairs and
    parameters:
      - name: json_object
        description: The object to make into a key:value pair
        multiline: true
        example: "{'key': 'value', 'key2': 'value2', 'key3': 'value3'}"
        required: true
        schema:
          type: string
      - name: split_value
        description: The way to split the values. Defaults to comma.
        multiline: false
        required: false
        example: ","
        schema:
          type: string
      - name: include_key
        description: Whether it should include the key or not
        options:
          - true
          - false
        schema:
          type: string
      - name: lowercase
        description: Whether it should be lowercase or not
        options:
          - true
          - false
        schema:
          type: string
  - name: run_math_operation
    description: Takes a math input and gives you the result
    parameters:
      - name: operation
        description: The operation to perform
        required: true
        multiline: true
        example: "5+10"
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: escape_html
    description: Performs HTML escaping on a field
    parameters:
      - name: input_data
        description: The input data to use
        required: true
        multiline: true
        example: $exec.field1
        schema:
          type: string
      - name: field_name
        description: The field to HTML escape
        required: true
        multiline: true
        example: my_unsafe_field
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: base64_conversion
    description: Encode or decode a Base64 string
    parameters:
      - name: string
        description: string to process
        multiline: true
        example: "This is a string to be encoded"
        required: true
        schema:
          type: string
      - name: operation
        description: Choose to encode or decode the string
        example: "encode"
        required: true
        options:
          - encode
          - decode
        schema:
          type: string
  - name: cidr_ip_match
    description: Check if an IP is contained in a CIDR defined network
    parameters:
      - name: ip
        description: IP to check
        multiline: false
        example: "*******"
        required: True
        schema:
          type: string
      - name: networks
        description: List of network in CIDR format
        multiline: true
        required: true
        example: "['10.0.0.0/8', '************/24']"
        schema:
          type: string
    returns:
      schema:
        type: string

# yamllint disable-line rule:line-length
large_image: data:image/png;base64,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
