walkoff_version: 1.0.0
app_version: 1.0.0
name: passivetotal 
description: Passivetotal example app 
environment: cloud
tags:
  - TI
  - Threat intel
categories:
  - TI
  - Threat intel
contact_info:
  name: "@frikkylikeme"
  url: https://github.com/frikky
  email: "<EMAIL>"
authentication:
  required: true
  parameters:
    - name: username
      description: Username of the user
      multiline: false
      example: "<EMAIL>"
      required: true
      schema:
        type: string
    - name: apikey
      description: apikey to use
      multiline: false
      example: "ee9618a507fce0b9abb8c6a57e250940qwec76ed0e3fbf304c98372ebbfa72ab"
      required: true
      schema:
        type: string 

actions:
  - name: add_artifact 
    description: Adds an artifact to a project 
    parameters:
      - name: project
        description: "The project's ID"
        multiline: false
        example: "59362b45-d69b-4ab1-bdb5-49c01fa7494d"
        required: true 
        schema:
          type: string 
      - name: artifact 
        description: "The item to use"
        multiline: false
        example: "google.com"
        required: true 
        schema:
          type: string 
      - name: tags 
        description: "The "
        multiline: false
        example: "malware, ioc, Alexa top 1m"
        required: false 
        schema:
          type: string 
    returns:
      schema:
        type: string
  - name: update_artifact 
    description: Updates an artifact, e.g. the monitor
    parameters:
      - name: artifact_id 
        description: "The artifact to update"
        multiline: false
        example: "59362b45-d69b-4ab1-bdb5-49c01fa7494c"
        required: true 
        schema:
          type: string 
      - name: monitor 
        description: "To monitor or not"
        multiline: false
        example: "true"
        required: false 
        schema:
          type: string 
      - name: tags 
        description: "The "
        multiline: false
        example: "malware, ioc, Alexa top 1m"
        required: false 
        schema:
          type: string 
    returns:
      schema:
        type: string
  - name: get_artifact 
    description: Gets an artifact saved to a project
    parameters:
      - name: query 
        description: "The artifact to update"
        multiline: true 
        example: "google.com"
        required: true 
        schema:
          type: string 
  - name: get_alerts 
    description: Updates a project
    parameters:
      - name: project_id 
        description: The project ID to look for
        multiline: false
        example: "59362b45-d69b-4ab1-bdb5-49c01fa7494d"
        required: true 
        schema:
          type: string 
      - name: artifact_id 
        description: The artifact ID to look for
        multiline: false
        example: "59362b45-d69b-4ab1-bdb5-49c01fa7494c"
        required: false 
        schema:
          type: string 
      - name: start 
        description: "The start time to start searching"
        multiline: false
        example: "2017-04-01 00:00:00"
        required: false 
        schema:
          type: string 
      - name: end 
        description: The end time to search from
        multiline: false 
        example: "2017-04-01 00:00:00"
        required: false 
        schema:
          type: string 
    returns:
      schema:
        type: string
  - name: update_project 
    description: Updates a project
    parameters:
      - name: data 
        description: The data to send
        multiline: true
        example: "{\n\t'project': '59362b45-d69b-4ab1-bdb5-49c01fa7494d'\n\t'name': 'Test project'\n}"
        required: false 
        schema:
          type: string 
    returns:
      schema:
        type: string
large_image: data:image/jpg;base64,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
