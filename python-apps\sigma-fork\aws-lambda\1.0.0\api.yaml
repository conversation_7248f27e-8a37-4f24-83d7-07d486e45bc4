app_version: 1.0.0
name: AWS Lambda
description: An app to interact with Amazon Lambda
contact_info:
  name: "@shalinbhavsar"
  url: https://shuffler.io
  email: <EMAIL>
tags:
  - MISP
  - AWS
categories:
  - MISP
authentication:
  required: true
  parameters:
    - name: access_key 
      description: The access key to use
      example: "*****"
      required: true
      schema:
        type: string
    - name: secret_key
      description: The secret key to use 
      example: "*****"
      required: true
      schema:
        type: string
    - name: region 
      description: The region to use
      example: "ap-south-1"
      required: true
      schema:
        type: string
actions:
  - name: list_functions 
    description: Lists all lambda functions
    returns:
      schema:
        type: string
  - name: get_function
    description: Returns information about the function or function version.
    parameters:
      - name: function_name
        description: The name of the lambda function, version or alias
        required: true
        schema:
          type: string
      - name: qualifier
        description: Specify a version or alias to get details about a plublished version of the function
        required: false
        schema:
          type: string
    returns:
        schema:
          type: string
  - name: list_aliases
    description: Returns a list of aliases for a lambda function
    parameters:
      - name: function_name
        description: The name of the lambda function, version or alias
        required: true
        schema:
          type: string
      - name: function_version
        description: Specify a function version 
        required: false
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: invoke
    description: Invokes a lambda function
    parameters:
      - name: function_name
        description: The name of the lambda function, version or alias
        required: true
        schema:
          type: string
      - name: invocation_type
        description: Invocation types
        required: true
        example: RequestResponse
        options:
        - RequestResponse
        - Event
        - DryRun
        schema:
          type: string
      - name: logtype
        description: Set to Tail to include the execution log in the response.
        required: true
        example: None
        options:
        - None
        - Tail
        schema:
          type: string
    returns:
      schema:
        type: string  
  - name: get_account_settings
    description: Retrives details about your account's limits and usage
    returns:
        schema:
          type: string
  - name: delete_function
    description: Deletes a Lambda function
    parameters:
      - name: function_name
        description: The name of the lambda function, version or alias
        example: my-function
        required: true
        schema:
          type: string
      - name: qualifier
        description: Specify a version or alias to get details about a plublished version of the function
        required: false
        schema:
          type: string
    returns:
        schema:
          type: string
large_image: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABLAAAASwCAMAAADc/0P9AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAC61BMVEXYZhPZaxvjklfuu5b118H66t/88uz89e/89O766+D228fvwZ/lmmTbcCPcdizuvJf99fD////++vjxx6jfgDvZaBfrsYf9+fb//fzabh/cdCn23crfgT3dejLhikvbcyb56d3YZxT00rr56d7abR7no3DuvJj78en+/PrefDXpp3fwxqf449P+/fvcdy7vwqHmnWj++/ntt5DuvprcdSryzrPtuZPzz7X9+PXstIv99vHacCLrsIX89O3qrH777uTZahnnonDmnmn559rYZxXlmWLnoG3gh0b//v7klVvlmmP34dHfg0DjkFT007v23MnhiEfhi0z12MP01r/ijlHhiUnz0bjghUL34M/yzLDll1///v3fgDz45NXxyKrmnGbYaBb56Nvww6LdeTDZaRjuvpvopXTcdiv77+btupXpqXvabBz88uvstY3qroL99/L88+zss4nabyHqrYDsto/78erbcSX9+PTpqHn77uX++vfopHLvwJ767ePnn2vwxKTdezPxyaz45dfefznkll344tLfgz/jkVbijE7hiUr22sfghUP3387449TefTb67OLbcyfZaxr78OjbcSTabR355tnefjj44tPfgj723cvghkX22sbghkTklFr34NDyyq7efTf45dbefDTvwaDdeC/77+ftuJLqq33bcibss4rabiDrr4T99/PcdCjopnbuvZncdy3noW/lmGHyy67z0Lb3383jj1P01Lz12cXhiEj12cT01b7ij1L23Mjz0Lfjk1jghEHyy6/lmGDxx6n56NzwwqLnoW767OHopnXtuZTpqnzstYzrr4PqrH/pp3joo3HmnmrwxaXxyq3efzrklVzjkVX118L11sDijU/z0bnyzbLuu5XqrYHrsonrsojsto7opHPvv5znoGzwxKPhi0vmm2X01L3klFnkl17yzbHijlDzzrTwxabtuJHrsIbpqn3mnWf33szlm2TpqXrijE3deTH55tjvv53xyKsztHjNAAAAAWJLR0QR4rU9ugAAAAd0SU1FB+ULCw4rBFEPgvsAAERoSURBVHja7d15gFfT/8fxKaJF+LRoiuSjqKhIRVo0GJlE2YpMNDKylDZEiBSyMy0K0SJKhEJZ0qLsIZKd7JIlO3/+vr6/r+8XQ6/b3M/nfM773Ofj75pz3ue8z3vu3HvuPXl5AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACDKlXeYssqW22dPFWrVa+xTU0SALBj2+22TyVZrdp1SALAhro71EslXX6N+iQCYECDHVNIparsRCoA3mtYjWL1b9vvTDIAnmu0C6Xq94rFjSzAb+ldKVT/1bgJCQH4bDfK1B/sTkIAHmvSlCr1B82akxKAv/agSP3JnqQE4K8W1Kg/admQnAB81YoS9Rd7kRSAr/amQv1F61ZkBeCpfahQf9WGrAA8tScFqtx70G1JC8BP7ShQ5exLWgB+2o/6VE77RuQFQMGyYn/yAqBgWdGhI4kBULCs6ERiABQsKzrz8VGAgmXGAWQGQMGyomklUgOgYFmxB6kBULCs6FJAbgAULCsOJDcACpYV1eqSHAAFy4qDSA6AgmXFwYVkB0DBsuIQsgOgYFnRIk16AKYKVteaoTq0SFasbqQHYKpgHZbka8vuXGIBFCw/HK4vsY4gPwAKlh96yILVk/wAKFh+OFLfdz+KBAEoWH44WhasY0gQgILlh2P1JVYvMgSgYPmhuixYvckQgILlh8r6Eus4UgSgYPnheFmw+pAiAAXLDyfIglXclxwBKFheSJ8oK9ZJ5AhAwfJDP1mw8puTJAAFywslJ8uK1Z8kAShYfugqC1azU8gSgILlhdJqsmKdSpYAFCw/DJAFq9ZppAlAwfJCwemyYp1BmgAULD+cKQtW+0bkCUDB8sLAQbJinUWeABQsPwyWBWtITRIFoGB5oclQWbF2IFEACpYfhsmCVbU+mQJQsLwwfHtZsc4mUwAKlh/OkQXr3IGkCkDB8sKI82TFOp9UAShYfthCFqyRBeQKQMHyQoMLZMW6kFwBKFh+uEgWrFGlJAtAwfJCq9ayYl1MsgAULD9cIgtW4xKyBaBgeWF0S1mxtiFbAAqWHy6VBWtMIekCULC8MLZYVqzLSBeAguWHdrJgXZ4mXwAKlheuKJIVaxz5AlCw/LCrLFg7ki8ABcsPh8qClbqShAEoWH7YUhasLUkYgILlhyv1JdZVZAxAwfLD1bJg7UrGABQsP4zTl1jXkDIABcsP18qC1Y6UAShYfrhMFqyincgZgILlhfR1smJdSs4AFCw/bCMLVv71JA1AwfJC4RhZsS4haQAKlh8ulgWrWUOyBqBgeaGksaxYF5E1AAXLDxfKgtV6W9IGoGB5ofQGWbG2IG0ACpYfzpcFq96N5A1AwfJC2XhZsc4hbwAKlh/OlgVryAQSB6BgeWHiUFmxhpE4AAXLDzvIgtVhOJkDULC8MHx7WbEGkzkABcsPZ8mCNbQJqQNQsLww4jxZsc4kdQAKlh/OkAWraSVyB6BgeaHBBbJiDSB3AAqWH06VBWtUAckDULC80Kq1rFhdSR6AguWH/rJgValL9gAULC80bCkrVj+yB6Bg+eEkWbAmFZI+AAXLCzcVy4p1AukDULD80EcWrMlp8gegYHlhSpGsWJXJH4CC5YfesmDdTP4AFCw/3CILVupYEgigYPnhGFmwjiaBAAqWH3rpS6ydySCAguWHW2XB6kEGARQsPxyhL7GmkkIABcsPPWXB2o8UAihYfrhNFqyiKeQQQMHyw+2yYk0jhwAKlh8OkQUrfzpJBFCwvFA4Q1asPUkigILlh4P0JdZMsgigYHmhZBdZse4giwAKlh8OlAWr2SzSCKBgeaH0BlmxdieNAAqWH/aQBatWW0YJoGB5oexOWbHuYpQACpYfZsuC1X4OowRQsLwwcStZse5mlAAKlh86yYLVoSOjBFCwvDB3a1mx7mGUAAqWH/aXBatzE0YJoGB5YcK9smLNZpQACpYf9pUFq2klRgmgYHlhXj1Zse5jlAAKlh/ulwWrSwGjBFCwvPBAa1mx5jNKAAXLD3vJglWtLqMEULC80LClrFh7M0oABcsPe8qCNamQUQIoWF6YmS8r1gJGCaBg+eFBWbBapBklgILlhTrFsmLdxigBFCw/PCQL1sMMEkDB8sPhRbJiLWSUAAqWHxbJgvUIgwRQsPzwqCxYqaMYJYCC5YfHZMF6nEECKFh+WKwvsXoxSgAFyw9PyIK1hEECKFh+WCoLVtEyRgmgYPnheFmxljNIAAXLD0/KglXcl1ECKFheSM+QFWsFowRQsPzQTxas/OaMEkDB8kLJSlmxnmKUAAqWH56WBavZKYwSQMHyQukzsmKdyigBFCw/PCsLVq3TGCWAguWFgtNlxXqOUQIoWH54Xhas9o0YJYCC5YWBg2TFeoFRAihYflglC9aQmowSQMHywtytZcXagVECKFh+GCYLVtX6jBJAwfLC8A6yYr3IKAEULD/sIwvWuZUYJYCC5YU558mK9RKjBFCw/LCdLFgjCxglgILlhba1ZMV6mVECKFh+qCELVrW6jBJAwfLCrNayYq1mlAAKlh9ekQVrlxJGCaBgeWF0S1mxGDyAguWJV2XBGlPIKAEULC+sKZYV6zVGCaBg+aGdLFjd04wSQMHywhVFsmKtZZQACpYfesiCtSODBFCw/HCkLFipKxklgILlh9dlwXqDQQIoWH54U19iXcUoARQsP1SXBestBgmgYPmhsr7EuoZRAlwVrBcWu3PkmgJzw3e8LFhvk2OAq4LlVssZZ0y1NXyXyZiK65BkQJAF6zcPL7Y0fOkTZUCXkmRAsAUrleozwdD4bSPDyW9OlgHhFqzUqDV2xq/kZBnOJWQZEHDBSm09xc4AXiyjaXYKaQYEXLBSp59mZgDrVpPRXESaASEXrNQbdr59d6EMpvW25BkQcsFKXWxmBEtHyWDeIc+AoAvWyDIzQ3i+DKZ9IxINCLlgpQaYGcKy8TKYc0g0IOiCNarAzBi+K4MZUpNMA0IuWKmnzYzhxKEymPfINCDoglXFzlHv78tgqtYn1YCQC1aqn5lBHL69DOZdUg0IumBNsrMXa50MZmgTcg2I7W1/C1bqSTOjOKe9DOZMcg2I7RWPC9ZkO+eQviODGV9GsgFx3e1xwUotNTOMDS6QwXxAsgFxLfC5YN1sZxw/lME8U0q2AXHvvhT7XLHsfH20VWsZzEdkGxDXrT4XrEfsjOPHMpiVJWQbEFM/nwtW6hMz4zi6pQzmU7INiKn0ZJ8L1md2BvIkGcyMNOkGxPS515dYdo79WqPvBq4n24C4+vtcsJbYGccvZDDducQC4iq72uOCVbTMzDhOKZLRVCbbgLhqPuFxxVpuZxzfksH0JNmA2CrV9rdgFd9kZhgP1dFsINmA+J68wduKtcLOKB4jgzmaVAMycSPrwsmeFqyWo80M4lE6mqtINSAjGu52au9HbndNf/uuv50h1I8vepBngGEHyjVu6Kj3tSFtLANQTqm+e9bGTjQ9ZTBfMuWAYXvINV7rNDPBdNMby3ZiygG7yu6Ui/w5M8Gku8tgXmXKAcNmyzVu6Kj3w2Qw+dOZcsCuiVvJRf6CmWAKx8hgvmLKAcM6hXTU+2r91LMhUw7YNXdruch3MBNMyS4ymBpMOWCYPrrH0FHvu8lgWm/LlAN2TbhXLvIXzQQTYWPZdkw5YNhdco2fO9BMMC/pjWVtmXLArnn15CI/30wwETaWfc2UA4btLtf4yAIzwRygN5bNYcoBu9rWkov8ZTPBRNhYNowpBwy7Q67xanXNBPONDKZDR6YcsGuWPur9YjPBzNUf+VrFlAOGfSXXeOMSM8G8IIPp3IQpB+yamS8X+TZmghmhN5ZtZMoBw6bJNT6m0Eww+8pgmlZiygG7Ihz1fpmZYOZdIIN5likHDPtSrvHL7Rz13kYG06WAKQfsOlwf9T7OTDAP6Kee85lywLAeco3vaCeYp2QwVeoy5YBdR+pDst40E0zDljKYb5lywLDX5Rrf0k4wtWUwkwqZcsCuDSEd9X6Tfur5JFMOGFY9pKPel8tgJqeZcsCuyvoS6xozwdTRl1ifM+WAYdfKNd7OTjBLZDDfMeOAYetDOup9md5YtpgpB+xKnxjSUe+fyWAeY8oBw74P6aj3R/UtuU+YcsCukpPlGn/FTjSPyGA+Y8oBwz4K6aj3xfoSaypTDthVWi2ko96fkME8xJQDhl0Y0lHvn+unnsuYcsDwJdaokI56v10Gs5wpBwz7IaSj3hfIYIrrMOWAXWXjAzrqPT1DBlObKQcMOzuko96/1RvLmjPlgF0ThwZ01HvJShnMXkw5YNj7IR31Pl9vLDuFKQfsGq6Per/HTDClz8hg2jDlgGHrQjrq/T791PM0phywa8R5AR31XnC6DGZfphww7IyQjnrfqJ96NmLKAbsa6KPe7zMTzMBBMpj9mXLAsFNDOur9npCeegIop1VIR73P3VoG8w1TDhjWP6Sj3u/WTz3rM+WAXRGOet/bTDAT7pXBHMCUA4atCOmo969lMOdWYsoBuyIc9b7ATDBz2stg9mDKAcP6yDXews5R77vLYEYWMOWAXVP0OaS3mQmmbS0ZzG5MOWCYPur9YTvB1JDBVKvLlAN2RTjqfaGZYGbpjWUHMeWAYY/LNd7TTjBfyWAOLmTKAbt66XNIjzITzMx8GcxhTDlgmD7q/XE7wUwL6akngHIW6kusXmaCWaM3lnVjygHDeso13ttOMF/KYLpziQUYpo96Tx1nJpjD9VPPI5hywDB91HsfO8H0COmpJ4ByIhz13tdMMEfq68UfmXLArghHvZ9kJ5qjZTDHMOWAYUEd9b4hpKeeAMqJcNR7fzvRVA/pqSeAcoI66r1ySE89AZQT4aj3D+1Ec7wM5gumHDDsWbnGW9s56n29DKaoL1MO2BXhqPczzASTPjGkp54Ayolw1PuNZoL5NKSnngDKiXDU+zozwZScLIP5mCkHDFsl1/iQmmaC6aqfev7ElAN2RTjq/X0zwZRWC+mpJ4Byhsk1XtXOUe8fhPTUE0A5HTvIRf6umWCCeuoJoDx91PuggWaCOTOkp54AyplznlzkP5gJpmx8QE89AZS3nVzj48vMBDM4pKeeAMppe4Fc5B+YCabJ0ICeegIo7yK5xkcVmAnmvZCeegIoJ8JR7x+ZCWb49jKYs5lywLBX5BpvXNdMMOfIYM4dyJQDdo1uKRf5p2aCGRHSU08A5V0q1/jPhWaC2SKkp54Ayhmrj3pfbyaYBvqp54VMOWBYO7nGL7dz1Psv+qlnKVMO2HWFPuq9splgWumnnhcz5YBhu8o1frOdYC7RTz1LmHLArkP1IVkbzAQT4ann90w5YNiWco0fbSeYX0N66gmgnCv1JdbOZoKJ8NTzMqYcMOxqucZ72Anm7ZCeegIoZ5y+xJpqJpignnoCKO9aucb3sxNMUE89AZTzmj7qfYqZYCI89XyTKQfsSl8n1/g0O9G8IYPZkikHDNtGH/U+3UwwQT31BFBO4Ri5xve0E01QTz0BlLNaX2LNNBNMhKee1zDlgF0lu8g1foedaPRTz3ZMOWDYy3KNN5tlJpgITz13YsoBZ9LH3fPF5Go3nLjf3b1KMvIDS2+Qi/x+O6Ojn3peSg4BjpSdv/J/S69Lp7mZ+JkvyTVeq62ZAQrqqSdg22uj/rz4tuqaiSJ4p1zkd5kZoQhPPV8hjwAH6v7N6advT4z/c1+Ua7z9HDODpJ96NmtIKgHZ/3Owx98tv4drxv7BE7eSi3x/M6MU4annReQSkPU/dv7h8ylXxz+/6hu5xjt0NDNO+qln623JJiDLVv3T+vsw9o+eq49672RmnCI89dyObAKyq06zf9xZdGzsH/6CXOOd65sZqfNlMPVuJJ+ArNrEx55axP5W+Yh75SI/wMxIRXjquQ/5BGTTLZtafwti//jn5Bo/t5KZsdJPPc+bQ0YBWfTQptZfi9jfKp+nj3rfw8xYRXjqOYyMArJHHAlzW+wG2sg13qXAzGjtENJTT8CeFZtefw/HbuABfdT7bmZGa7h+6rmKnAKypVUzsf4Wxm5iL7nGq9U1M15n6aeeTcgqIEtOVeuvZ+wmGuqj3g8yM14Rnno+T1YB2XFjPbn+jordSG3ZxsF2jnrXTz2bViKvgKxYp7/8e0zsRprny0YOMzNiEZ56DiCvgGyI8OZMKtUrdjMPyjbG2LnEkn9Ep0YVkFlAFrwboV6lesdupk6xbKSbmTFrpZ96Pk1mAZlXMD5KwUodF7uhh2Qb3dNmRq2/DKZKXXILyLgBkepV6ovYDS0rko2sNTNqEZ569iO3gEwraRytYBX1jd3UItnIjnbGbYUMZlIh2QVkWL9URL/GbupR3ciPZsbtJn1L7gSyC8is9OSoBSu/eezGHpONvGFn5PrIYCanyS8go25LRfZx7MaO1Y1cZWbkpuhbckvJLyCjnohesJr95KC1t+wM3RIZzHfkF5BJR6U2Q/yvuy91sX3ClQhPPY8lw4AMWrQ5BSsDx8EcLxt5287gPS6DeYwMA5zeh/mjd2I3eIKL7ROu9NIj9gk5BmTM8s2qVxk4DiZ9ooPtE87cKoNZRI4BmTI6f/MKVmpd7Cb1tq/8682M30I9YlPJMiBD+m9mvUoNiX1wfclK2cgldgawpwxmP7IMyIwGtTa3YKXei91oVxfbJ1zRu9iKppBnQEa8s9n1KlU19gnNpdVkI7/YGcLbZTAPkmdAJky4d/MLVurd2M0OcLF9wpVDZDDFdcg0IAPeq0C9Sg2NfRxMwemykS3MjGHhDBnMnmQaEN/ArSpSsFJnxm74TAfbJ5zZWz/1nEmuAQ7qxt8aXxa7Ug6SjZxjZhRLdpHB3EGuAXGVjqpYwUp9ELvpwQ62TzhzoH7qeQrZBsT0dAXrVQaOg2ky1MH2CWeF/wYZzP1kGxBP4c8VLViprrEb17f7Oww3M5L3yWBqtSXfgFgWVLheZeA4mOH6JMTBZkay4E4ZzF3kGxDL8RUvWKlPY7d+joPtE87MlsG0n0PCATEcEaNeZeA4mBHnyUaeNzOWE/X+kP3JOCCGx+IUrAwcB7OFbKNpJTOD2UnfkutIygEVdkusepWB42AaXCAbGWBmNOduLYPpRM4BFfZQvIKVqhy7B7842D7hzN0ymM5NSDqggsYWxyxY8Y+DadVaNvK0mfGM8Br5bLIOqKAVqbjiHwfzsYPtE87cFdItOcAvrZrFLljxj4MZ3VI20s/MiM6rJ4PZg7wDKuTDVHzxj4P51cH2CWful8F0KSDxgAq4UT+hGyn/xWexuxHhRtqTZsb0AX1L7kAyD6gAvc384bUujoP5wsH2CWfukMFUq0vqAZttrn6Rr1vetfLfPBS7IxFOcV1qZlRn6fuCe5N7wGYbHOXe0WsujoN5y8H2CWf2DOmWHOCLsvFRLgXS3R0cB3Oo/sNzsZlxnakPpT2E7AM207N6i3npv/7ZYS6OgznGwfYJZx6UwbRIk37AZimpItfVfb/9u8Ix8t/Vjt2ZH11sn3BljX7qeRv5B2yWb+WqGvT/e7JX6+NgmsfuzdUOtk84s59++kr+AZsjfaJcVav+cymmj4PZK3Z3jnCxfcKVw/VTz4VkILAZbtMH1vz+6abdXBwH09PB9glnFslgepKBwGZ4Qq6pfX7/pxGOg2kTuz/d9PaJZWYG91F9vXgUKQhEpu9y/+GElz30Pz4t9p+oevvEcjvDe7QM5nFyEIjsM7mifvnfPy7Tx8HsG7tHh7jYPuHKsfoSqxdJCESkX4bJH/2Hf36APg6mUdwuFc5wsH3Co7+4e5OFQER95Ho66Y//PMJxMC/E7tNBLrZPuLJUX2IdRxoCkYyWb48U9f3Tf+gU/ZlihUXYPvGUnSHWxz32IQ+BSPRXiZf8+T9EOA7mm9i9OtDF9glXTtC35PqSiEAE27be7FvC++vjYOrH7ZaT7ROuRNiYu4JMBCJ4R66lR/76XyIcB/Ni7H652D7hTL+QbskBuROh+Kwt95/2lf/n3NjHwUTYPvGcmVEuWSmD6U8uAtJ7Ffn8SYTjYF6K3bPZDrZPOPN0SLfkgFyZOLRCH5jTx8GMLIjdNRfbJ1wprSaDOZVsBITn9cGlJX/z3yIcB/Ny7L652D7hzICQbskBOfrFP0quo/l/+x9dHAfjZPuEKwWnB3RLDsiN+XIVnf73f9pFOA5mdeze6e0TVeubGernQ7olB+RCoX549fw//Fd9HEzjkrjdc7J9wpWBg2QwZ5GRwCbojyJUbfIP/zXCcTDbxO7fXXr7xEAzg60PUhtSk5QE/tntcg2994//d5r8v2Nin7gXYfvE+WYGu4l+ILsDKQn8I330fPs5//ifIxwH81rsHrrYPuHMsJBuyQHOPSZX0Bab+N/6OJjLY5+417aWg+0TrgzvENAtOcC1W/Tu61mb+O8RjoMZF7uPLrZPOLNPSLfkAMeWyPXz8Sb/fw/5/3eM3cdZeofqxWYGfM55Ad2SA9waK29CFV+/yR9wpP6Q5pWxe/mVg+0TzmwR0i05wKmTYn8GUx8Hs2XsXjrZPuFKgwsCuiUHuBRhr7r60PgGfYl1Vex+utg+4cxFId2SAxz6Ra6dz+TPqC5/Ro/Y/YywfeIyO78lQrolB7hzo/7r5Ef5QyrrS6xrYvf0SwfbJ5x5JaRbcoAz+hH7dxF+yrXyp7SL3VMn2ydcGd0yoFtygCtzt5cL57YIP2a9/ClFO8Xuq4vtE85cGtItOcCRVXLdTIqybiIcB/Nq7L5G2D7xppmBH+vijSYgLGXj5bLZO9IP+lQfBzM9dm9fd7B9wpl2MpjuaRIU+KNnM/V4veRk+ZNeid1bJ9snXLkipFtygAslVeSieTbij/pIv5DYMHZ/XWyfcCaoW3KAA3vLNTMo6rmCEY6DqRG7v062T7hyqIs3moBwpGfIJTM48g+7UP6s1tvG7rGL7RPObCmDeYMcBf6rWybPz4pw8M52sXvsZPuEK2+GdEsOyLqecsGcsxk/7Qd94l7b2NeELrZPOKNvye1KkgL/cWVmK0yELRJfx+7z9y62T7gyLqRbckCWPS6Xy4eb9fPOjvNp+IicbJ9wRt+Se5s0Bf5NbwTKH71ZP3CiPg5mWOxeO9k+4cpl+pZcXxIV+M0XcrWs2Myf+L78iR06xu21k+0TrqSvk8FcSqIC/zI6P+O/3YfrN6nvid1vJ9snXIlwS+56UhXIy/tYrpUlm/0z18mf2blJ7EssF9snXIlwS+4SUhXI21Z/87LXZv/QEfo4mI2xe+5i+4QzF+tbcj+RrIA+uOWxCvzUM+RPbVopbs+dbJ9wdonVWAbzC8mKxJugr4WOqMCPjXAczH2x++5i+4QzQd2SA7JkmFwnkyv0NaZT5c/tUhC37062T7gS4ZbcFqQrEi7Cml9QoR/cSt8amx+79y62Tzijb8nVu5GERbJtlKukSknFfnJ/ByfuRdg+scrMVES4JXcOCYtEi/B3yNMV/NEN9XEwe8fuv4vtE87oW3JDapKySLID5RoZWVDRn70iM8dabJKT7RMe/Xn+HimLBCtZKZfI8xX+4TcVZ+n22B+52D7hjL4lt/1wkhbJdZhcIVVj/EXVR/70FrGPg3GyfcKVCLfkBpO0SK7bs/o3yBR9HMxtsUP40MH2CWf0LbmhTchaJNXaLG+8XCJ//sOxY3CyfcKVCLfkziRtkVSPyOXxTqyfv0xfYi2MHYTePlGlrpkZ0bfkxpeRt0gmfbxUs1nxWtCfMu0ZOwon2ydciXBL7gMSF8nUWy6O/jFb6KW/VX5U7DBcbJ9wRt+SG1VA5iKJxsptB/G/Gaf/6DwmdhxOtk+4EuGWXFdSF0n0q1way2O3sVBfYvWK3YiL7RPOBHVLDsiYCPd+jovfyhOykd6x23CyfcKjafmU5EXyXCQXxqIMtPJ5ykFZdLF9wpmTZDA/F5K9SJp5Fzi4H54XZW/qF7HbcLJ9whV9azF1AumLpNlHLovvMtLOAhcn7rnYPuGMPnVtcpr8RbLM3d7RfZ/0DNnQSbEbcbJ9wpUIt+Qqk8BIllXONi99q0/cax67Eb194nE7c6P3x91MAiNRInzd8tsMNRXhEzYfx27EyfYJV27RwWwghZEk9zn4fvHv5rs4ca+ng+0TzhwjgzmaFEaC1H1GLokBGWusVDf2YexGnGyfcOVHHczOJDGS4yC5IM7N4Hc6n3Vx4p7ePtHHzvzcKoPpQRIjMSI8uXs3g80VnJ7lz9j8Rm+fKO5rZoKO0JdYU0ljJMVr+niWjB7n97yDE/ecbJ9wRt+S+5I0RlLo5bAuo+0NHOSgQSfbJ1zpprfbTiGPkQxvytVQq0FmW9S7vuKfuBdh+0R/M1OU7i6DmUYiIxn0U/NTM9zi3K0dnLgXYfvEKWbm6BB9vTidTEYSXCFf/Wj5U6bbHKbPE6sftw0n2ydcKdS35L4ilZEE+uXaFRlvc3gHB88lI2yfOM3MLOmdJ80akssI33T5+ZKiLDz/1x+HiH/iXoTtE2eYmaaSXWQwd5DMCN8lciE8lIVW57g4cW+jPmexkZl52i3rZxoB/ntAn3KQldeEt3Nw4p6T7ROulN4gg9mddEbodOHIzou1Tk7cc7F9wpk99O6TtuQzwjZB/2mWpa8J62/Ixz9xL8L2iffNTFXZnTKYr0lohO3unH1/d5aLE/dcbJ9w5gB9S24OGY2QTRwqF8GT2Wpb3+2Pf+LehHtlI2fbmaytZDB3k9II2Wy5BE7O2hlSo12cuPe1bGPQQDOz9Y0MpkNHchrhKu0il8DT2Wv9UgffkY+wfeIHM9MV4ZbcPSQ1wqX39owsyF7rTk7cc7F9wpn9ZTCdm5DVCFWEDxqcmc3233Zwx79tLQfbJ1yJcEtuI2mNUB2mn6Fl9Rf2FS5O3KvhYPuEM/vKYJpWIq8RqNtzvUtpVwfHTUfYPvGRmRmbp7fb3kdeI0zjcr6v51D9rfJjYzfyimyjcV0zc9ZGBtOlgMxGkPRZLFn/mMEbDl4McrJ9wpUIr37OJ7MRIn15k4HTtoQr9SXWJ7EbeVW28XOhmVnby8F2W8BDb8nUfyr7nbhadmJR7DYibJ9Yb2bWGurrxW/JbYRnjVzGLk6VWevixL12so3L02bmrbaD7baAd/Q+8wdddONaB98PdLJ9wpXm+TKYBWQ3QhPhT4tlLvqhD3HNwIl7evvEzXZm7kEZTIs0+Y3AXOTg5lEUEU7ci3+lF2H7xAYzM1dH35L7nPxGWCLsQDzKTU/0dvviOrEb2TJH31XNioccbLcFvPK1N0lfOEZ2pXbsRiJsn9jZzNwt07fkFpLhCMnc7f35s2J1ysHjSr19ooed2ftMBvMIKY6Q3OPRXsoIJ+7tFbuRcS62T7jyqIvttoA3yprKjO/nrjcRTtw7JXYjevvEfnbm7zEZzGckOcKhT4yq5vD9jggn7rWJ3YiT7ROuLNaXWI+S5QhF6TN+fdQuwol7p8VtI32dbGSanRl8QgazhDRHKPRt7nOdfgYuwol7+8ZuZBt9b3+6mRn8XF8vLiPPEYb0DN/Ovopw4l6juG1E2D6xp505PF4Gs5xERxgu0we4D3fbowgn7r3g4Loyf6aZOXzSxXZbwAc7ymRf57pLnXQNjX3iXoTtE3eEdJVcm0xHCN7Ut7gbuO5ThBP3vondyMt6+8QsM7P4rYvttkDuHSNT/VT3nYpw4l79uG1E2D5xv5lZjHBE21PkOuzTH4dq9pP7XkU4ce/F2I28pK8t25qZx6ddbLcFcu1tP29+7Otgq0WE7RN3mZnHCJvp2pDtsG66/JpSUd9c9GtePbn+XordyIs5P9gsg551sN0WyDF9Sl+O3qm7X3ZsZEHcNiJsn9jfzEwWnC6DeY58h20RzrV71NuevRy7kW9kGx06mpnLjQ622wI5tbtM8tdz1bU7HLyRHeE7YJ3MzOXAQQ622wI5NOI8meOLc9W3Wc1k31bHbuQFvX2iiZnZXOVguy2QQ3q7Uw5P6NtTdq5xSeyKrbdPzDYzm0622wI5M7GzzPAnc9e7mfrEvW1iN/KcZ1+qiGWYg+22QM7Mlgl+ci5PDZ4muzcmdvciHBe0h5n5dLLdFsiRCA/Cu+ayf2v0iXuvxW6kjWyjS4GZGf06pOtF4M92c7DVKZb9HNxii7B9YjczMzpHP0R5ibyHTRG+r/JDbnt4uD5xb1zsRp7y6oP2MW3n++8goKK28f+Rfg/ZxR1jt9GwpWzkIDNz2rZWQNeLwB91l7m9Q667eKQ+DubK2I3Ulm0cXGhmUmuEdL0I/E9l/SbHiJx38mjZyS1jt9Fcb584zMysztK35FaT+zBIn9buwbuyG/Ql1lWxG1ku22iRNjOtX4V0vQhE/2urtQ9fI6kuu9kjdht19PaJbmbmdXTLgK4Xgd/tKvN6Lxt/uKauid3IQ7KN7nYusV51sN0WcGwnuWPAk0MLrpXrr13sNpbp7RNrzczsmpCuF4H/d6nM6gf96Oh6fajxTrEbWeRg+4QzX4Z0vQj8JsLmo2V+9DR9ouzpq7EbeVT/4fmjmbk9PKTrReA3NRzcy86QT/WJe9NjN/KYbOQNO5PbI6TrRSDSNwo+8aWvJSfLvr4Su5FjXWyfcOXIkK4Xgby8u2RG3+xPZz/SJ+41jN3IE7KRt+xM7+shXS8CwzvIjF7qT29Lq8ne1ojdyFJ9VXKcmfndENL1ItBJ5vN1Pj1HulDvcd02diPHy0betjPB1UO6XkTSlTWV+fypT/0tHSX7u13sRk7Q2yf6mpnhyiFdLyLpXrL2Rv8P+lDjtnHbiLB94lc7U6y3237BOoANpTfIbL7Qs0vC8bLHX8dupJ/ePnG9mTleH9L1IpLtYnsf/j5bfwpnTtw2SlbKRi4xM8dhXS8iydJjZC6f7VufJw6VfR4Wu5GuevvET2Zm+dOQrheRZJfJVO4w3LtOv687HftQ4wjbJ34xM8sRttt+zFqAATvKTD7Lv04P3172+p7YjQxwsX3ClY9Cul5EculNhfVu9LDb6xycmBHhnMYtzMxzhOvFD1kN8N4bMo/b+NjtEfrEvY2xGznTZjH/ex+EdL2IpNLfHml2ipcdP0Ouv6axn20OHCQbOcfMTBfo7bbvsB7guXYyi/f0s+MN9Acm7ovdyGDZxpCaZqb6h5CuF5FM0+X3c4vqeNr1D+X661IQt40mevvEe2bmOsJ223WsCHhNHwL1pa9db6VP3Jsfu5H3LO75+CfvhnS9iCR6QC/6R73tfH8Hr0BG2D4x2MxsR9hu+z5rAh6738FBylkT4Tv0e8du5BzZxtAmZqZbXy9Wrc+igLci7A1Y7HH3V8jeTyp0MERnmpnvCNeL77Iq4K39Zf5e7vMBUDfpE/cWxG5kCwfbJ5zR220HDWRZwNd7Gp1l/p7gdQB9ZP9bxC64EbZPDAjpkvoH1gU8dYDM3pP9PsR8ij5x77bYjfwi2xhVYGbK35HBjC9jYcBLBXfK7P3I8xCWyAgejt1GhO0TT5uZ8wjXix+wMuCll2XujvT90mGZvsRaGLuRS2QbVeqamfSgrheRJCWNA7if8ZmM4bHYbYzW2yf6mZn1CNeLH7E24KHvHXygJet66eNgdordyK8Otk8487EMpnEJiwPeSU+WmbuDgTAekVHcFbuNsXr7xJNm5j3C9eL3rA54Rx9Vd6+FF8sWyzBOjN/I27KRyWkzE6+vF38uZHnAN/ow4H1NxPGECqN4Yuw2ImyfWGpm4iNcL65necAzV+nvT55mIpDPZSBT4jfylmzkOztTr68XL0+zQOCXXWXW7mUkkttVIG/Gb+NQfW9/sZmpj3C9WJkFAmNJmz/TSCjfqEjWZqCRYxxsn3BGXy/ezAqBV16VOTvNSCSFu7j4oteP+hLrEzOTH+F68U2WCDwS4VNSy4yE8qSMJCNnwVwtm/nMzvTrk5JeZ43AIzVkxvawEsrN8pt0GWnmCH1VMtXM9Ee4XtyZRQJvzLsgmIQ9SkbSJzMN9ZQNPWQnAa4O5xcWEuAuma/VrYTS28Fnkv+tm2yoaJmZBFirL7GuYZnAEx2HBLMPco182tk5Q98DTXeXg7bcTgpca/e0JCSO3AiQus7KzsFXZCj7Z6qpQ2RTxXXMpMBr+npxJxYKvDBwK5mtnxoJ5TT5sZTWDTLVVuEMOWy1zeRAhOvFV1kp8ML5Ds7zc0TfjKuRucYOko3lNzeTBIfpYKazVOCB0htkrl5oJJT68tiq4gyWkBK5RTX1lJksKBwjg3mFtQIPXCwz9Vwr51bpUzTezmRzB8rmmp1iJg1W62AasliQ+7sX+lfri1auFUfJUB7NaHv62rSNmTyIcL1Yg9WCnFsv87TD8GCuFW/NbIN7yAZrnWYmEfQZJK23Zbkg13aUefqClWvFFjKUzzPbYpk+GO05M4kQ4XpxO5YLcuxYmaX1GhkJRX/k+eBMf+t3tmyyfSMzqfCSvl5sy4JBbm0ps/R+K6E8JkPpmukmJ24VzPVppOvFfVgwyKmp4TzoukaG0jTzp653ko0O6WgmGV6UwZw3hyWDXPpS5uhXVkLpI0M5O/ONzt1atvqNmWSIcL04jCWDHLpJnphSZOV9uIb58piybFzr7C/XeNX6ZtJBv1TaoSOLBrnzlczQdlZC+SU3D+wm3BvMNrZ/XS/KNwVSq1g0yJkH5LvCmd1pmUUj2subcbOy0rB+fdHMiwJ5eS/IYDo3YdkgV+6X+bmllVCGyVD2zE7D8+rJll8ykxAj9PXi8ywb5Co9z5PpeayRUAYOdXF+agWr/sgCMynxnH7UWomFA1//ALjWSij6JZmsfZS8bS3Z9stmUiLC1/2fZeEgJ+rrR/LrjYRSUkWGcmXWGr8jnO+J5eW1kcF0KWDpIBf0PsGMv8qSLfrzc8dnr/FZzWTrq80kRYTnME+zdJADBfpNjIutxPKdDOWQLLa+p2y9cYmZtHhKBlOlLosH7l0YzsX/m7n9o2ym3LOa2sZMWkQ4BPxbFg/c3/bZJZzH8T1yfKd4mmx/TKGZxFghg5lUyPKBa5/qPYITjYTStyjHoayRbzilLjOTGPp1rdSTLB+4dnk4b+3WlqG8l+Ue7Cd7cHnaTGYsl8FMTrN+4NZSmZX31jQSyrbywVbWv0F4uLzGS40zkxp19CXWUhYQ3Kouk3JfK6GcIUP5MOt90HfRdrSTG0tkMN+xgODUkfrEASvHJ8ztII8AnenBeKbeNJMcy/T14mKWEFzSVwR3WAllsAxluYNevB7Oe+R5eY/LYB5jCcGhKfJ3qJlT1iPsfz3UQTc26Eusq8ykx6M6mE9YRHBHbxx61Uoo82UoRzvph74p2MNOfjwig1nEIoIzEXYzH24klPTPMpTKTjqiDxlLXWMmQRbqYKayjODKXjIdd7USSjcZygxHm4auDedz03l5PWUwD7GM4EgD/QWnna3Eov8SW+2oJ+tlT4p2MpMin+tgprCQ4Ib+rOTVVkK5RYZyeoGrP05PDOfGYF7e7TKYB1lIcGJ4B09u+2SAPldxtrO+fC/7kj/dTJIskMEU12EpwYUdZC5eZ+VdsebyLZIhw511puRkObCvmEmS9AwZTG2WEhwYqM/3/d5KLPrrxF877M1HsjfNGppJk7319WJzFhOy7/xwPkF+ozwywekLRqXV5NDWMJMmJStlMHuxmJD9VXVDOIe86GN/LnHaH/0R19bbmkkUvSO32SksJ+T+75amZVb+uO0sn727vTFcOkoO7nZ2frM9I4O5n+WELEuPkWl4gJVYfpCh9PauR7XamkmV+3Qwp7GgkF0nyCzsMNxIKCX6lpHrV3TLxnv1FCCeCK+V78uCQnbdLJNwfyuh9PPw4OqzZZ/azzGTK7N1MI1YUcimxTIH65nJwe4ylhOc92niUNmpYWaSZeKgcH67wSb9nbndrYSivyhQrcR9r97Xf3J3NJMt94QUDAyaGtCj6mNkLBfmoFfDt5fdWmUmXeZuLYPpxKJC9uhX776yEor+aOqgSrno1zp93mMTM/lytw6mPqsK2aLPyCwy80brpZ6eqzinvezYRjMJM+FevTX35dguXn/cQBYnytsznG/MtWomnx7k6HGcPnVsfJmZjPk65UZ+9dkjWJ/4yyKXJ46mjrQSy+7ebsNuqz+PeJ+ZlJlXL+VKrS0msETxR21k0rxhJZSa58nf2aNz1bcP5TB3KTCTM7un3NmqG2sU/zNC313ZYCWWTh5/3TPChex8M0kT4Xoxc4qGsUrxX2d5uDO8ggr0KzDLcte7/rJzjeuayZo7Ui69wDLFf9TXm2ousxLLy17/cTtan6K2t5m0adjMacXqykLF/9OvuR1caCSU9CQZyxG57N8K2b1JhWby5iunBav99axU/PuvKP3u/WorsegTtXL7VXq93y21wEzizMx3WrE+Y6niNx8E9OxKH/P5bW472Ed2cHLaTOZMc1qwUj+yVpGXV9JYZspLVmLZWYYysjS3PdTvDaVuM5M6a4qdFqy3WazIy/tUvxU20UosS2QsZ+a6i71lFx+2kzv7OS1YrXk7EXl5l4fz3v10+Rt/65y/XqxPpE4tNJM7x7n9m5Dto8hbKtPk3ppWYvlYxrIu9518XHayp53s6eG0YLF7FHnVZZrcZSWUeXLvdWsPDnr4RC/Mo8xkTy+nBWsayzXx9PJpbeYAlH1kLE/50M1HZDcft5M/R7ssWItYr4mnr+nNHElcX37Ss2isD/3UX89PHWomfza4LFiPsF6TTj9lz59pJZaNMuH386OjerNYbzsZVJ2CBXcelElyqZVQIhyu3MuPnn6ul+ZxZjKoMgULzjSUL+MWHW4lloNkvj/hS1dvl13tYyeHjqdgwZWnZI68ZSaWyTKW13zp6gLZ1eK+Zsb9BAoWHGmgv8F2lZVYxslQTvbmOwjpGbKzJ5lJovSJFCy48ZxMkavNxKIPgj3Qn85+Kzvb0syzjgjvdlGwkAk19VFNla3Eoh93nuvRiTQlK+XI9zeTRoW3U7Dgwg4yQ1qY+dSJftzp1bHKT+v9urPM5NFRxRQsZN/ArWSGbGMlFv24s71Xp9uV3iDH/kM7mfQ1BQvZ94NMkGpmTkQ4Vcbyjl8dfjagV6LyStpRsODBL/ndrMQyQR5G2Myzv7DK7rRWYjcZzZcULGRZV5kfTc0cnP6+jGWFb11+Xp+60MhOMpW8kE/BQjYV/izzY7aZX/BNZSxX+NbnifoO4jpL+XTkzRQsZNGTMj2qmvki7QAZi4cnrqySnR5S01JCpdfudwEFC9miXwG728zfI7vIWI71r9dz9fG17xvLqUqLN+5+SZ8YbqVg4e8tlKulnplbKPrVPC/3kw0L6BrXTVZSsJJLv8myu5lY9N2T733s9gT9osHZFCwKFv5lqlwrzU6xEsuPMpZn/NxPpvdbDhpIwaJgIcqBcq+YiWVXGYunB8HOkbvHUj9QsChYyLtJvv1VvCacWKo28bTr28mCNb4sUXlJwcLfqi1Xip1jwb+y+7izrf4e2YUULApW4rVqLRfKNVZi2VbGUquBt52vIedhVAEFi4KVdPpV4WPMxLKvjOUifzs/S//m+IiCRcFKuBHt5TJ500osc+VhhMXNPe7+K3ImGpdQsChYybZOrpKHzcTyoozlC5+7P7qlzT1kFCx4dFHiz/kySmkXGcuRXgfwquz/z4XJSU0KFsp7V66Rg82skY+svzA7Vn9eeD0Fi4KVYAXj5RJZbSWWCOdlLfU8BP2xzsvTiclNChbK+UCukC4FVmJZav9i8Qp53I+do4soWMi4ksZygexhJphHAtgV0EPGcDMFi4KVWP0CeuNWv8J9p/8Xi4fqD9dtoGBRsBIqfblcHveYCeaLIL7PsqWM4mgKFgUroT7XH+btaCWWhvLgg3stxHKlvsTamYJFwUqmJ+Ti+NpMLBfJWPY1EcfVMo4eFCwKViIdpc/vbGsllkbyzINmrUwEMk5fYk2lYFGwkmiR5VeF/+JuGctXRiK5VkayHwWLgpVAU+Smn/yZVmIZOFTFUtTXSCivyYJVNIWCRcFKnuVyZfxqJpaXZCy7WgklfZ2MZRoFi4KVOPqxWtHhVmIpqSIX+Y9mJmYbGUv+dAoWBStpnpLr4q2A1vi1diamcIyMZk8KFgUrYRroT4hfZSaY72QsCwxNzepUODcXKVjIjDPkqrjVTCwbZCzVSgxNTckuMp47KFgUrESJcNLwODPB6P0ZA0xNzsv6ZNtZFCwWcZK8L9dECzNfXtpJ7s/oPNHU5JTeIGfnfgoWizhBBm4ll8RhZoJZIWN539j06F0atdoGn6MULPzXmXJFVDFz1+cBeTxWvTnGpqfsTjk/d1GwkBilo+SCONBMMO/IWE41N0H6/J/2c0JPUgoWfve0XA9Ny6zEMreD3AQw2twETdR/su9PwUJCFP4sl8NsM8GskrE8aHCKvpFRdegYeJZSsPAfC+RqqFrfSiwRzv051OAURTgvshMFC8lwvFwMd5uJ5UAZy+sm5+gFGVfnJhQsJMFCuRbqNbISS1r/dTvO5CSNuDegP9spWIjhaLkUtjMTi/561IlGjx59TkZ2biUKFsJ3S0gvfujP0h9kdJrmyY8+GzozkoKFCntILoRLAiq+IwuszlObgE7lpmChosYWq3VQvMZMMF/KRb3R7ETpHfyGdvdSsFBB+s27L8zEcr0svkOG250p/YnFanUpWAhbq2ZyGVxjJpi9ZCz7GJ6qhi2DvUFHwUJEH8pF8LiZWG6U96Vbn2Z5rmrLuTq4kIKFkOk1nnrTTDBnyVg+Nj1ZzeU5IalDKFgI2Tq5BB42E8vEzvLcnzW2Z0ufxNYiHWyqUrAQ5RW118wEoz/qtcT4dNWRDxVS3ShYCNe7cgFMMnNXpK7+kPDO1udL75nrHuwlFgULET5tYOe507cB/XX7T5bJz9Wn1lKwEKoBevN0qZlgLpfBrLc/Y5/JIHekYCFQJY1l+t9nJpgjZCwrA3jm/6iMMvUjBQth0n9EDbLzAYA3ZDAvhzBnj8kw36BgIUjpE2XyrzITzJSigIrvJhyrL7GuomAhRLfJ1B9i5zvhryblE8L6AzpvUbAQIp36dt68069Eth8Rxqwt1ZdYx1GwEJ4fQzpO+H4ZzO6hzJv+Av/bFCyEZ5FM/F/MxFLzPHkYYcNQ5u0EOW9FfSlYCI2+S50/00ww+ti+S4OZuAjPSn6lYCE0ywNK+4KmMphl4cxcPxls/vUULIRltPxUSdFOZoK5UC7hYwKaupKVAX2Fn4KFSPrLpO9tJpbCSTKYhSHNXVd9ztFPFCyEpEGtgPYf6tvQ1wX1DYPSagE9L6FgIYJ3ZMobSoAdZTD9wpo9/dJ6620pWAjHBH3wuZ3PlHwS0jcnIik4XYa8BQUL4XgvpG/t9pbB/BDa/OmPq9a7kYKFUEwcGtBpBtPld4O3bhLaBA4cJCfwHAoWQvG8TPcqJWaCuUQGc1Z4MzhYv7hek4KFMJSOCujQ83nyeWfrtuFNYRN9jfweBQthmC+T/fQCM8F8LYPZK8Q51HchOwynYCEEhT/LZN9oJpj68qCy4rEhTuJwfUDbYAoWQrBApnrV+maCmS2D+TLMWTxHBj40rGcNFKyk0h9UGmYmlgi343qFOYsj5Bd1UmdSsGCfPl6m/RwzwayWwVQPdR63kKE3rRRSvBSshNLnrhjaJT05uWe3N7hAxj6AggXrbtGv+s8yE0xlGczJhcHO5C8y+FEFFCwYt0Sm+cd2gjlaBjM/3Jls1VpG35WCBdvGFge0DeAauWLPLQt4LvUe/yp1KVgw7SSZ5H3sBLM8aXuR/mx0yyR9V4eClUSz5Pl9hk61aygX7L0dg57NX+VkTgrnFh4FK4k+lCn+eEjBnJH0v+9TJ1CwYNeN+lH4lWaCmdA+oOedFfO2nM7JwXwcmoKVQPp9joftBKPf/10R+nzqwyVTSylYsGqufmPWzj7LgVvJYK4IfkbfkmPwHQULVg0O6SbtszKYReHP6KFyEFKLKViwqWy8zO69zQRT0lgGsyEBc/qGHIXHKFiwSV+TjLJzvMwhMpjbkzCnP+pLrE8oWLCopIrM7WftRHOzDGabRMzq1XIcPqNgwaK9ZWoPsvM9kitlMNXqJmJW1+pLrKkULNiTnhHSiyw9ZDB7JGRee8qReIiCBXu66ZOh7LzIMlZuQKo6kXn9j6JlFCwE+Jt4HzvB7BnQZ57jXjl3l2OxnIIFa/TjpFp2zu/bVn4Kqta8xMysfl5aXIeCBWM+k2n9i51gngspmLgK9c3J2hQs2KLfOssfbSaYuR1kMDMTNLcHyYKV35yCBVP6yKQ+yU4wZ4f0FcL4SnaR4/EUBQuWjM6Xj5L6mgmmtItcoEcmanYP1AeLnELBgiEfy5ReYieYrol5fy5qBb9BjkgbChbs0E/VDB2QnB4jg6mcsPndQz8CPo2CBTO2COma5HMZzMGFCZvfsjvlmDxHwYIVE86T+XyEnWhulcF8lLgZni3HpH0jChaMGCbTuYWdT39PlcGcXpC4GZ6oP7/6AgULRrJ5qMzmQ+xEow9eeDGBc9wppDdFKVjJ9rw+IbjETDAN5QaNIcMTOMdzt5aT/A0FCxaUjpK5PN9ONDVkMHclcpb3l+NStT4FCwbMD+mmjz5ZsVmrRM7yhHsD/1OZgpUQhStlJj8f0oXEKwmd57vkyJxbyXJ8FKyE0F8fqdrETDAD5fODojoJned59eREv0TBgvdul3n8np1gXpLBvJXYib5fjs3IAsPhUbCSQR9S0H6OmWAiHPxzVGJn+gH9/tXLFCx47hGZxe/YCeZ7Gcy1CZ7qO4I+SYiClQj6LPNms0L68/bJBM/1rGZyeFZTsOC13jKH+9sJ5lh9CVGS5MnWR3M0tjs+FKwkGFssDyi43k40+rv0HyR6tmfKtwAMH4dNwUqCk0L6mPBO8rv0gyole7qnyekeY/bLOxSsBGjYUmbwcSFV3x0SPt9r5AV16jUKFrz1i8zfz+wE84C8p1xvTtInfD854ZenjYZGwQqffvEu9aOdaPRnU9skfsYPl381p8ZRsOCpfWT2fmcnmJry7V5DJytmTQ855TtSsOCnudvL7L3NTjT3yGCmMeV5R8pRSr1JwYKXVsncnWTnmVHBeBnNLUx5Xt7Rcpi2pGDBR2V6iX9rJ5rdQl2IGbZBX2JdRcGCh+4L6dWy9CQZzVqm/DfV5UD1oGDBPxG+bDDATjSXyWBOTDPnv6msL7GuoWDBOwfJvLW0L7ynjGZvpvz/XSuHqh0FC979DTVD5u1gO9HcEvbH6TJqvRyrop0oWPBMt6AOqtM7uJ9nyn//VXWiHKxXKVgw9zfUOXaCuV6+I9dhOFP+O/2Zw/zpFCx45UqZtLXa2onmqZCqb9aVnBzi0UIUrKA9LnP2QzvB3FhLBdO6LVP+Px/pz8w2pGDBI1fIl2Bb/mQnmnUhfTbVgdJqcsBqULDgkS9kxq6wE8zEzvKx1xqm/I8ulNPfelsKFrwxWn4rt6ivnWiel8vvIab8z5dYo+SQbUfBgjcuCWmJ170h0LfjsuiHoJ65ULACF+FIzV52otlbBvMwU/4XEV58/5qCBU9sJ7P1aEPRXC6juYwp/6uzQzrwm4IVtgnnyWw9wk40a2UwKwuZ87+aOFQO2zAKFrwwTObqZENfNthSRrMbU17e+3LYOnQ0FRAFK8G/XBfYiWYKhxFWyHD9fexVFCx4YKPM1ColdqLRZ4Pew5T/Hb3btnMTChZyLsIenKftRPOTPAq2/Qjm/O+M0HcyN1KwkHMH6u/KPHqcGXpH2e5M+d87Qw5d00oULORYycpUorQ8hTn/ew30MbrPUrCQY4clq16lfmXK/8mp+i7WG3Z0p2AF6faEFazDmfJ/0qp1gvKAgmXTuITVq8eZ8n/Wn4IFz92asIK1mCn/Zw1bUrDgtUMTVq9acBjhppxEwYLXeiesYH3KlG/KTcUULHhsbHGy6tUzdZnzTepDwQJ/AnjjB6Z80/SbmBQs5Eyj1smqV1WbMOfcI6BgmXV+wi6wzmLKlWVFFCz4alGy6lWteUy59DgFC76qmqyCdQczrvWiYMFTDZJVr4qvZ8ojuJWCBT/VSVbBaseMR7GQggU/HZ6sgvUoMx5JTwoWvNQ8UfXqaiY8ms8pWPDSxETtc+/GhEeUiC8O9WCe7WmcoHo1icMIo3oyCfnAhxwNeipBBetppjuq9IwE5MM3zLM9S5NTr8YXMN2RfZuAhFjLNNtTckNiCta7zPZm5EX49wrqcZquRQOSUq8G1WeyN8P84BPiKybZorozElKwDmSuN0dptcDzoeg4JtmkOrUSUa8W8WXkzfN94AnxIFNsNTOTsBfr5wlM9OZJ7xp0Qmw/iym26oP88OvVA0zz5mowMuCEKGYTsWHdQv/IzCKuryqgb7hpUXQf02vZA+1CLldDB3D/qkLGhrrnpdlHTK5xR05rHWhyVhs8nOmtoAlfBJkSK6cytfbVX7/Pkuq3h+WNrzZewdVVHEeE9xr0oNkFzCsQ6rX3/ScHdCrFVg9+T7kCgtZkypWLgzC1AZMJAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJMP/AQfM6fNgx0/TAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDIxLTExLTExVDE0OjQzOjA0KzAwOjAwcuiEjAAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyMS0xMS0xMVQxNDo0MzowNCswMDowMAO1PDAAAAAASUVORK5CYII=