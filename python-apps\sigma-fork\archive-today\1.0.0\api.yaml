walkoff_version: 1.0.0
app_version: 1.0.0
name: Archive.today
description: Archive.Today app 
environment: cloud
tags:
  - archive 
categories:
  - archive 
contact_info:
  name: <PERSON>
  url: https://github.com/peter-clemenko
  email: <EMAIL>
actions:
  - name: archive_target
    description: Archives the target URL
    parameters:
      - name: target
        description: URL of the target to archive
        required: true
        schema:
          type: string
    returns:
      schema:
        type: string
      example: "Google.com"
large_image: data:image/jpg;base64,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
