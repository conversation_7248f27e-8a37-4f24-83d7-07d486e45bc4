# Sooty app
Reference : [Soo<PERSON>](https://github.com/TheresAFewConors/Sooty)

## Actions

| No. | Action | Description | Parameters |
|-----|--------|-------------|------------|
|1 | Urlsanitise | Sanitise URL's to be safe to send in emails. | Url
|2 | urlDecoder | Inverse operation of URL encoding. | Url
|3 | SafelinksDecoder | Office Safelinks Decoder | Url
|4 | UnshortenUrl | Unshorten URL's that have been shortened by external services. (Limited to 10 requests per hour) | Url
|5 | Cisco7Decoder | Decode Cisco7 Passwords. | Password
|6 | DnsLookup | Perform DNS lookups | Domain_name
|7 | HashText | Hashes plain text to unreadable text. | Text
|8 | WhoIs | Perform WhoIs Lookups | Ip

ReverseDnsLookup function is yet to be added.
