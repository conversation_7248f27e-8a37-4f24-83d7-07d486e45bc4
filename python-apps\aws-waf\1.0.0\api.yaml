app_version: 1.0.0
name: AWS WAF 
description: An app to interact with Amazon WAF 
contact_info:
  name: "@frikky<PERSON><PERSON>"
  url: https://shuffler.io
  email: <EMAIL>
tags:
  - Network 
categories:
  - Network 
authentication:
  required: true
  parameters:
    - name: access_key 
      description: The access key to use
      example: "*****"
      required: true
      schema:
        type: string
    - name: secret_key
      description: The secret key to use 
      example: "*****"
      required: true
      schema:
        type: string
    - name: region 
      description: The region to use
      example: "ap-south-1"
      required: true
      schema:
        type: string
actions:
  - name: block_ip_waf
    description: Blocks an IP in the AWS WAF
    parameters:
      - name: ipset_name 
        description: The IP Set to ue
        required: true
        multiline: false 
        example: 'my-ipset'
        schema:
          type: string
      - name: ip 
        description: The IP to block 
        required: true
        multiline: false 
        example: '*******'
        schema:
          type: string
    returns:
      schema:
        type: string
large_image: data:image/jpg;base64,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
