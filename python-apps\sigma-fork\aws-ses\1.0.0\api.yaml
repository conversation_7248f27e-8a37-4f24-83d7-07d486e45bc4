app_version: 1.0.0
name: AWS ses
description: An app to interact with Amazon EC2
contact_info:
  name: "@shalinbhav<PERSON>"
  url: https://shuffler.io
  email: <EMAIL>
tags:
  - Communication 
categories:
  - Communication 
authentication:
  required: true
  parameters:
    - name: access_key 
      description: The access key to use
      example: "*****"
      required: true
      schema:
        type: string
    - name: secret_key
      description: The secret key to use 
      example: "*****"
      required: true
      schema:
        type: string
    - name: region 
      description: The region to use
      example: "ap-south-1"
      required: true
      schema:
        type: string
actions:
  - name: send_email
    description: Composes an email message and immediately queues it for sending.
    parameters: 
      - name: source
        description: The email address that is sending the email.
        required: true
        schema:
          type: string
      - name: toAddresses
        description: The recipients to place on the To line of the message
        required: true
        multiline: false
        example: '<EMAIL>,<EMAIL>'
        schema:
          type: string
      - name: ccAddresses
        description: The recipients to place on the CC line of the message
        required: false
        multiline: false
        example: '<EMAIL>,<EMAIL>'
        schema:
          type: string
      - name: bccAddresses
        description: The recipients to place on the Bcc line of the message
        required: false
        multiline: false
        example: '<EMAIL>,<EMAIL>'
        schema:
          type: string
      - name: replyToAddresses
        description: The reply-to email address(es) for the message. If the recipient replies to the message, each reply-to address will receive the reply.
        required: false
        multiline: false
        example: '<EMAIL>,<EMAIL>'
        schema:
          type: string
      - name: subject_data 
        description: The subject of the message
        required: true
        multiline: false
        example: 'Test email'
        schema:
          type: string
      - name: data_option
        description: Type of message to be sent.
        required: true
        example: '1'
        options: 
          - Text
          - HTML 
        schema:
          type: string  
      - name: content
        description: The content of the message, in text format or in HTML format.
        required: true
        multiline: true
        example: 'The content of the message, in text format.'
        schema:
          type: string
      - name: charset
        description: The character set of the content.
        required: true
        example: 'UTF-8'
        options:
          - UTF-8
          - UTF-16
          - ISO-8859-1
          - US-ASCII
          - UTF-16LE
          - UTF-16BE
        schema:
            type: string
    returns:
      schema:
        type: string
  - name: verify_domain_identity 
    description: Adds a domain to the list of identities for your Amazon SES account in the current AWS Region and attempts to verify it.
    parameters:
      - name: domain
        description: The domain to be verified.
        required: true
        multiline: false 
        example: 'domain name'
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: verify_email_identity 
    description: Adds an email address to the list of identities for your Amazon SES account in the current AWS region and attempts to verify it.
    parameters:
      - name: emailAddress
        description: The email address to be verified.
        required: true
        multiline: false 
        example: '<EMAIL>'
        schema:
          type: string
    returns:
      schema:
        type: string
large_image: data:image/png;base64,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
