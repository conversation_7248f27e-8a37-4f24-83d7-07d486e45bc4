app_version: 1.0.0
name: RSS
description: Allows access to read RSS feeds
contact_info:
  name: "@frikkylikeme"
  url: https://shuffler.io
  email: <EMAIL>
tags:
  - Intel 
categories:
  - Intel 
actions:
  - name: get_rss
    description: Gets the available fields
    parameters:
      - name: url 
        description: The JSON to handle 
        required: true
        multiline: false 
        example: 'https://timesofindia.indiatimes.com/rssfeedstopstories.cms'
        schema:
          type: string
    returns:
      schema:
        type: string
large_image: data:image/png;base64,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
