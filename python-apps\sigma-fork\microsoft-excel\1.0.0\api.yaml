app_version: 1.0.0
name: Microsoft Excel
description: An app for Microsoft Excel using Graph Api
contact_info:
  name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
  url: https://www.shuffler.io
  email: <EMAIL>
authentication:
  required: true
  parameters:
    - name: tenant_id
      description: The tenant of the OAuth client
      example: "***"
      multiline: false
      required: true
      schema:
        type: string
    - name: client_id
      description: The client id to use
      example: "***"
      multiline: false
      required: true
      schema:
        type: string
    - name: client_secret
      description: The secret value to use
      multiline: false
      example: "***"
      required: true
      schema:
        type: string
actions:
  - name: get_excel_file_data 
    description: Gets data from all cells in an excel file as a list. If CSV, returns it as a CSV list. Max 25.000 lines in total due to timeouts.
    auth_not_required: true
    parameters:
      - name: file_id
        description: The file id of the file
        multiline: false
        required: true
        schema:
          type: string
      - name: to_list 
        description: Whether the output should be a list or not
        multiline: false
        required: false 
        options:
          - true
          - false
        schema:
          type: string
      - name: sheets 
        description: The sheets to use. Comma separated.
        multiline: false
        required: false 
        schema:
          type: string
      - name: max_rows 
        description: The maximum number of rows to return
        multiline: false
        required: false 
        schema:
          type: string
    returns:
      schema:
        type: string 
  - name: get_user_id
    description: Returns all users
  - name: get_files
    description: Returns all file information present the user's one drive 
    parameters:
      - name: user_id
        description: The user id to identify user
        multiline: false
        required: true
        schema:
          type: string
  - name: list_worksheets
    description: Returns all worksheets present in a workbook/Excel file
    parameters:
      - name: user_id
        description: The user id to identify user
        multiline: false
        required: true
        schema:
          type: string
      - name: file_id
        description: The file id of the file
        multiline: false
        required: true
        schema:
          type: string
  - name: add_worksheet
    description: Add one new worksheet with specified name
    parameters:
      - name: user_id
        description: The user id to identify user
        multiline: false
        required: true
        schema:
          type: string
      - name: file_id
        description: The file id of the file
        multiline: false
        required: true
        schema:
          type: string
      - name: name
        description: name of the worksheet
        multiline: false
        required: false
        schema:
          type: string
  - name: delete_worksheet
    description: Delete worksheet 
    parameters:
      - name: user_id
        description: The user id to identify user
        multiline: false
        required: true
        schema:
          type: string
      - name: file_id
        description: The file id of the file
        multiline: false
        required: true
        schema:
          type: string
      - name: name
        description: name of the worksheet to delete
        multiline: false
        required: true
        schema:
          type: string
  - name: insert_or_update_data
    description: Insert or update values in the file 
    parameters:
      - name: user_id
        description: The user id to identify user
        multiline: false
        required: true
        schema:
          type: string
      - name: file_id
        description: The file id of the file
        multiline: false
        required: true
        schema:
          type: string
      - name: sheet_name
        description: name of the worksheet
        multiline: false
        required: true
        schema:
          type: string
      - name: address
        description: Range/Address of the cells
        multiline: false
        required: true
        example: 'A1:B2'
        schema:
          type: string
      - name: value
        description: updated values 
        required: true
        example: 'row1column1,row1column2;row2column1,row2column2'
        schema:
          type: string
  - name: clear_data
    description: Clears data of specified range
    parameters:
      - name: user_id
        description: The user id to identify user
        multiline: false
        required: true
        schema:
          type: string
      - name: file_id
        description: The file id of the file
        multiline: false
        required: true
        schema:
          type: string
      - name: sheet_name
        description: name of the worksheet
        multiline: false
        required: true
        example: 'Sheet1'
        schema:
          type: string
      - name: address
        description: Range/Address of the cells
        multiline: false
        required: true
        example: 'A1:B2'
        schema:
          type: string
    returns:
      schema:
        type: string  
  #- name: convert_to_csv 
  #  description: Converts and xls(x) file to csv
  #  parameters:
  #    - name: file_id
  #      description: The file id of the file
  #      multiline: false
  #      required: true
  #      schema:
  #        type: string
  #  returns:
  #    schema:
  #      type: string 
large_image: data:image/png;base64,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
