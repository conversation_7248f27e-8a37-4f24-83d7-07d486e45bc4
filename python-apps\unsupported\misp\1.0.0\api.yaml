walkoff_version: 1.0.0
app_version: 1.0.0
name: misp
environment: onprem
description: MISP implementation with WALKOFF
tags:
  - TI
  - Threat intel
categories:
  - TI
  - Threat intel
contact_info:
  name: "@frikkylikeme"
  url: https://github.com/frikky
  email: "<EMAIL>"
authentication:
  required: true
  parameters:
    - name: apikey
      description: The apikey to use
      example: ""
      required: true
      schema:
        type: string
    - name: url
      description: The URL to target
      example: "https://localhost:9001"
      required: true
      schema:
        type: string

actions:
  - name: simplified_attribute_search
    description: Search query in attributes
    parameters:
      - name: data
        description: The IP, domain, url etc to search for.
        example: '*******'
        required: true
        multiline: false 
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: simplified_warninglist_search
    description: Search query in warninglists
    parameters:
      - name: data
        description: The IP, domain, url etc to search for.
        example: '*******'
        required: true
        multiline: false 
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: simplified_event_search
    description: Search query in attributes
    parameters:
      - name: data
        description: The IP, domain, url or other IOC to search for
        example: '*******'
        required: true
        multiline: false 
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: attributes_search
    description: Search query in attributes with JSON based on https://www.circl.lu/doc/misp/automation/#post-attributesrestsearch
    parameters:
      - name: data
        description: The full query to search for
        example: '{"value": "example.com"}'
        required: true
        multiline: true 
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: warninglist_search
    description: Search query in warninglist with JSON based on https://www.circl.lu/doc/misp/warninglists/#check-individual-values-for-warning-list-hits
    parameters:
      - name: data
        description: The full query to search for
        example: '["*******", "********"]'
        required: true
        multiline: true 
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: events_search
    description: Serch query in events
    parameters:
      - name: data
        description: The query to search for
        example: '{"value": "example.com}'
        required: true
        multiline true:
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: events_index
    description: Return events index filtering by data
    parameters:
      - name: data
        description: The query to search for
        example: '{"value": "example.com}'
        required: true
        multiline: false
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: event_edit
    description: Update event with json data
    parameters:
      - name: event_id
        description: Event id to update
        example: "1"
        required: true
        multiline: false
        schema:
          type: string
      - name: data
        description: The updated json to use
        example: '{"value": "example.com}'
        required: true
        multiline: false
        schema:
          type: string
    returns:
      schema:
        type: string
  - name: attributes_downloadsample
    description: Makes MISP samples available in shuffle as files
    parameters:
      - name: md5_list
        description: Md5 file you want to download
        example: "3520b8c2eacee8a506498c5ce7d02d3b"
        required: true
        multiline: true
        schema:
          type: string
    returns:
      schema:
        type: string
large_image: data:image/jpg;base64,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
